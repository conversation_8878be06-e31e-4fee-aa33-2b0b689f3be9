# 工人操作视频打分工具

基于YOLOv8 pose的视频录像打分工具，可以识别工人操作设备的动作并与标准动作对比，同时支持语音报告转文字并计算相似度。

## 功能特点

1. **动作识别与打分**
   - 使用YOLOv8 pose模型检测人体关键点
   - 与标准动作序列对比计算相似度分数
   - 支持实时摄像头输入
   - 提供详细的动作质量分析和改进建议

2. **语音报告分析**
   - 录制工人语音报告并转为文字
   - 与标准总结报告对比计算相似度分数
   - 支持关键词匹配和语义分析
   - 提供文本质量评估和建议

3. **综合评估系统**
   - 动作和语音的综合打分
   - 生成详细的评估报告
   - 支持HTML格式报告导出
   - 提供改进建议和训练指导

## 项目结构

```
test-yolov8/
├── src/
│   ├── pose_detection.py      # 姿态检测模块
│   ├── action_similarity.py   # 动作相似度计算
│   ├── speech_recognition.py  # 语音识别模块
│   ├── video_capture.py       # 视频采集模块
│   └── main.py               # 主程序
├── data/
│   ├── standard_actions/     # 标准动作数据
│   ├── training_data/        # 训练数据
│   └── models/              # 训练好的模型
├── utils/
│   └── helpers.py           # 辅助函数
├── reports/                  # 评估报告输出目录
├── logs/                     # 日志文件目录
├── requirements.txt          # 依赖包列表
├── setup.py                 # 安装脚本
├── demo.py                  # 演示脚本
├── run.sh / run.bat         # 快速启动脚本
└── README.md               # 本文件
```

## 快速开始

### 1. 环境安装

```bash
# 克隆或下载项目
# cd test-yolov8

# 运行安装脚本（推荐）
python setup.py

# 或手动安装依赖
pip install -r requirements.txt
```

### 2. 创建示例数据

```bash
# 创建示例训练数据
python src/main.py --mode demo

# 或直接运行数据创建脚本
python data/training_data/create_training_data.py --mode sample
```

### 3. 运行程序

```bash
# 交互式模式（推荐新手）
python src/main.py

# 或使用快速启动脚本
./run.sh        # Linux/Mac
run.bat         # Windows

# 功能演示
python demo.py
```

## 使用方法

### 交互式模式

```bash
python src/main.py
```

进入交互式界面，按提示选择功能：
1. 动作评估 - 录制并评估工人操作动作
2. 语音报告评估 - 录制并评估语音报告
3. 查看综合评分 - 显示综合评估结果
4. 保存评估报告 - 导出详细报告

### 命令行模式

```bash
# 动作评估
python src/main.py --mode action --action equipment_check --duration 5

# 语音评估
python src/main.py --mode voice --report equipment_check_report --duration 10

# 演示模式
python src/main.py --mode demo
```

### 参数说明

- `--mode`: 运行模式
  - `interactive`: 交互式模式（默认）
  - `action`: 动作评估模式
  - `voice`: 语音评估模式
  - `demo`: 演示模式
- `--action`: 标准动作名称
- `--report`: 标准报告名称
- `--duration`: 录制时长（秒）

## 系统要求

### 硬件要求
- 摄像头（用于动作录制）
- 麦克风（用于语音录制）
- 内存：至少4GB RAM
- 存储：至少2GB可用空间

### 软件要求
- Python 3.8+
- OpenCV 4.8+
- PyTorch 2.0+
- 其他依赖见 requirements.txt

### 操作系统
- Windows 10/11
- macOS 10.15+
- Ubuntu 18.04+

## 模型训练

详见 `data/training_data/README.md` 中的训练说明。

### 预训练模型
系统使用YOLOv8预训练的pose模型，首次运行时会自动下载。

### 自定义训练数据
1. 录制标准动作视频
2. 使用姿态检测提取关键点
3. 创建标准报告文本
4. 更新配置文件

```bash
# 录制真实动作数据
python data/training_data/create_training_data.py --mode record
```

## 评估指标

### 动作评估
- **整体相似度**: 0-1分，基于关键点位置相似度
- **分段评分**: 将动作分为多个阶段分别评分
- **关节点分析**: 各关节点的表现和一致性
- **评分等级**: 优秀(0.9+)、良好(0.8+)、中等(0.7+)、及格(0.6+)、需要改进(<0.6)

### 语音评估
- **文本相似度**: 基于TF-IDF和序列匹配的综合相似度
- **关键词覆盖**: 重要操作词汇的匹配程度
- **长度比例**: 报告长度的合理性
- **语义分析**: 内容的完整性和准确性

### 综合评分
- 动作评分权重: 70%
- 语音评分权重: 30%
- 最终等级评定和改进建议

## 常见问题

### 安装问题

**Q: 安装依赖时出现错误**
A:
```bash
# 方法1: 使用简化安装脚本（推荐）
python install_deps.py

# 方法2: 使用最小化依赖
pip install -r requirements_minimal.txt

# 方法3: 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 方法4: 手动安装核心包
pip install ultralytics opencv-python numpy torch torchvision

# 如果是M1 Mac，使用特殊处理
arch -arm64 pip install -r requirements.txt
```

**Q: Python版本兼容性问题**
A:
- 推荐使用Python 3.8-3.11
- Python 3.12+可能存在兼容性问题
- 如果必须使用Python 3.12+，请使用 `install_deps.py` 脚本

**Q: difflib错误**
A: `difflib`是Python标准库，不需要安装。如果出现此错误，请使用更新后的requirements.txt文件。

**Q: YOLOv8模型下载失败**
A:
```bash
# 手动下载模型
wget https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n-pose.pt

# 或使用代理
export https_proxy=http://your-proxy:port
python -c "from ultralytics import YOLO; YOLO('yolov8n-pose.pt')"
```

### 硬件问题

**Q: 摄像头无法打开**
A:
- 检查摄像头是否被其他程序占用
- 确认摄像头权限设置
- 尝试更改摄像头ID（0, 1, 2...）

**Q: 麦克风录音失败**
A:
- 检查麦克风权限
- 安装音频驱动
- 在Linux上可能需要安装: `sudo apt-get install portaudio19-dev`

### 使用问题

**Q: 姿态检测不准确**
A:
- 确保光线充足
- 避免背景干扰
- 保持适当的拍摄距离
- 穿着对比度高的衣服

**Q: 语音识别效果差**
A:
- 在安静环境中录音
- 说话清晰，语速适中
- 检查麦克风质量
- 可以尝试离线识别模式

## 开发指南

### 添加新的标准动作

1. 录制标准动作视频
2. 提取姿态关键点序列
3. 创建动作描述文件
4. 更新配置

```python
# 示例：添加新动作
from src.pose_detection import PoseDetector
from src.video_capture import VideoCapture

detector = PoseDetector()
capture = VideoCapture()

# 录制动作
frames = capture.capture_frames(duration=5.0)
poses = []
for frame in frames:
    pose_data = detector.detect_pose(frame)
    if pose_data:
        key_joints = detector.extract_key_joints(pose_data[0])
        poses.append(key_joints)

# 保存动作数据
action_data = {
    "action_name": "新动作",
    "description": "新动作的描述",
    "poses": poses
}

import json
with open('data/standard_actions/new_action/poses.json', 'w') as f:
    json.dump(action_data, f, indent=2)
```

### 自定义相似度算法

```python
# 继承并重写相似度计算方法
from src.action_similarity import ActionSimilarityCalculator

class CustomSimilarityCalculator(ActionSimilarityCalculator):
    def calculate_pose_similarity(self, pose1, pose2):
        # 自定义相似度计算逻辑
        return custom_similarity_score
```

### 扩展评估指标

```python
# 添加新的评估维度
def analyze_action_quality(self, standard_sequence, actual_sequence):
    result = super().analyze_action_quality(standard_sequence, actual_sequence)

    # 添加自定义分析
    result['custom_metric'] = self.calculate_custom_metric(
        standard_sequence, actual_sequence
    )

    return result
```

## 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境设置
```bash
git clone <repository-url>
cd test-yolov8
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 代码规范
- 使用Python 3.8+语法
- 遵循PEP 8代码风格
- 添加类型注解
- 编写单元测试

### 提交规范
- 清晰的commit信息
- 一个PR解决一个问题
- 包含必要的测试
- 更新相关文档

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至：[<EMAIL>]

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 基础姿态检测功能
- 动作相似度计算
- 语音识别和文本分析
- 综合评估系统
- 交互式用户界面

---

**注意**: 本工具仅用于教育和研究目的，请确保在使用过程中遵守相关法律法规和隐私保护要求。
```
