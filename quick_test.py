"""
快速测试脚本
验证核心功能是否可用
"""

import sys
import os

def test_basic_imports():
    """测试基础导入"""
    print("测试基础Python模块...")
    
    try:
        import json
        import time
        import difflib  # 这是标准库，应该总是可用的
        print("✓ Python标准库导入成功")
    except ImportError as e:
        print(f"✗ Python标准库导入失败: {e}")
        return False
    
    return True


def test_core_packages():
    """测试核心包"""
    print("\n测试核心依赖包...")
    
    packages = [
        ("numpy", "NumPy"),
        ("cv2", "OpenCV"),
        ("torch", "PyTorch"),
        ("ultralytics", "Ultralytics")
    ]
    
    success_count = 0
    
    for module_name, display_name in packages:
        try:
            __import__(module_name)
            print(f"✓ {display_name}")
            success_count += 1
        except ImportError as e:
            print(f"✗ {display_name}: {e}")
    
    return success_count >= 3  # 至少3个核心包可用


def test_yolo_model():
    """测试YOLO模型加载"""
    print("\n测试YOLO模型...")
    
    try:
        from ultralytics import YOLO
        
        # 尝试加载模型（这会自动下载）
        print("正在下载YOLOv8模型（首次运行需要时间）...")
        model = YOLO('yolov8n-pose.pt')
        print("✓ YOLOv8 pose模型加载成功")
        return True
    except Exception as e:
        print(f"✗ YOLO模型加载失败: {e}")
        return False


def test_custom_modules():
    """测试自定义模块"""
    print("\n测试自定义模块...")
    
    # 添加src目录到路径
    sys.path.append('src')
    
    modules = [
        ("pose_detection", "姿态检测"),
        ("action_similarity", "动作相似度"),
        ("video_capture", "视频采集")
    ]
    
    success_count = 0
    
    for module_name, display_name in modules:
        try:
            __import__(module_name)
            print(f"✓ {display_name}模块")
            success_count += 1
        except ImportError as e:
            print(f"✗ {display_name}模块: {e}")
    
    return success_count >= 2


def test_pose_detection_basic():
    """测试姿态检测基础功能"""
    print("\n测试姿态检测基础功能...")
    
    try:
        sys.path.append('src')
        from pose_detection import PoseDetector
        
        detector = PoseDetector()
        print("✓ PoseDetector初始化成功")
        
        # 测试关键点名称
        if len(detector.keypoint_names) == 17:
            print("✓ 关键点定义正确")
        else:
            print(f"✗ 关键点数量错误: {len(detector.keypoint_names)}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 姿态检测测试失败: {e}")
        return False


def test_similarity_calculation():
    """测试相似度计算"""
    print("\n测试相似度计算...")
    
    try:
        sys.path.append('src')
        from action_similarity import ActionSimilarityCalculator
        
        calculator = ActionSimilarityCalculator()
        print("✓ ActionSimilarityCalculator初始化成功")
        
        # 测试基础相似度计算
        pose1 = {'left_shoulder': (100, 200), 'right_shoulder': (200, 200)}
        pose2 = {'left_shoulder': (105, 205), 'right_shoulder': (195, 205)}
        
        similarity = calculator.calculate_pose_similarity(pose1, pose2)
        
        if 0 <= similarity <= 1:
            print(f"✓ 相似度计算正常: {similarity:.3f}")
            return True
        else:
            print(f"✗ 相似度计算异常: {similarity}")
            return False
    except Exception as e:
        print(f"✗ 相似度计算测试失败: {e}")
        return False


def create_minimal_demo():
    """创建最小演示数据"""
    print("\n创建最小演示数据...")
    
    try:
        # 创建必要目录
        os.makedirs("data/standard_actions/demo_action", exist_ok=True)
        os.makedirs("data/training_data/sample_data", exist_ok=True)
        
        # 创建简单的动作数据
        demo_action = {
            "action_name": "演示动作",
            "description": "简单的演示动作",
            "poses": [
                {
                    "frame_id": 0,
                    "keypoints": {
                        "left_shoulder": [100, 200],
                        "right_shoulder": [200, 200],
                        "left_wrist": [80, 300],
                        "right_wrist": [220, 300]
                    }
                }
            ]
        }
        
        import json
        with open("data/standard_actions/demo_action/poses.json", "w", encoding="utf-8") as f:
            json.dump(demo_action, f, ensure_ascii=False, indent=2)
        
        # 创建简单的报告数据
        demo_report = """演示报告：
设备运行正常，检查完成。

关键词：设备,正常,检查"""
        
        with open("data/training_data/sample_data/demo_report.txt", "w", encoding="utf-8") as f:
            f.write(demo_report)
        
        print("✓ 演示数据创建成功")
        return True
    except Exception as e:
        print(f"✗ 演示数据创建失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 50)
    print("工人操作视频打分工具 - 快速测试")
    print("=" * 50)
    
    tests = [
        ("基础导入", test_basic_imports),
        ("核心包", test_core_packages),
        ("YOLO模型", test_yolo_model),
        ("自定义模块", test_custom_modules),
        ("姿态检测", test_pose_detection_basic),
        ("相似度计算", test_similarity_calculation),
        ("演示数据", create_minimal_demo)
    ]
    
    passed = 0
    critical_failed = False
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
                if test_name in ["基础导入", "核心包"]:
                    critical_failed = True
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            if test_name in ["基础导入", "核心包"]:
                critical_failed = True
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{len(tests)} 通过")
    print("=" * 50)
    
    if critical_failed:
        print("❌ 关键测试失败，请先解决依赖安装问题")
        print("\n建议:")
        print("  python install_deps.py")
        print("  pip install -r requirements_minimal.txt")
    elif passed >= 5:
        print("🎉 基本功能测试通过！")
        print("\n可以尝试运行:")
        print("  python src/main.py --mode demo")
        print("  python demo.py")
    else:
        print("⚠️ 部分功能不可用，但基本功能正常")
        print("\n可以尝试基础功能:")
        print("  python -c \"from src.pose_detection import PoseDetector; print('姿态检测可用')\"")
    
    return not critical_failed


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
