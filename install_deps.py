"""
简化的依赖安装脚本
解决常见的安装问题
"""

import sys
import subprocess
import platform
import os


def get_python_version():
    """获取Python版本信息"""
    version = sys.version_info
    return f"{version.major}.{version.minor}.{version.micro}"


def install_package(package_name, version_spec="", use_mirror=False):
    """
    安装单个包
    
    Args:
        package_name: 包名
        version_spec: 版本规范
        use_mirror: 是否使用镜像源
    """
    cmd = [sys.executable, "-m", "pip", "install"]
    
    if use_mirror:
        cmd.extend(["-i", "https://pypi.tuna.tsinghua.edu.cn/simple/"])
    
    if version_spec:
        cmd.append(f"{package_name}{version_spec}")
    else:
        cmd.append(package_name)
    
    try:
        print(f"安装 {package_name}...")
        subprocess.check_call(cmd)
        return True
    except subprocess.CalledProcessError as e:
        print(f"安装 {package_name} 失败: {e}")
        return False


def install_core_packages():
    """安装核心包"""
    print("安装核心依赖包...")
    
    # 核心包列表（按安装顺序）
    core_packages = [
        ("numpy", ">=1.21.0,<1.25.0"),
        ("opencv-python", ">=4.5.0,<5.0.0"),
        ("torch", ">=1.13.0,<2.1.0"),
        ("torchvision", ">=0.14.0,<0.16.0"),
        ("ultralytics", ">=8.0.0,<=8.0.34"),
        ("scikit-learn", ">=1.0.0,<1.4.0"),
        ("matplotlib", ">=3.5.0,<3.8.0"),
        ("pandas", ">=1.3.0,<2.1.0"),
        ("Pillow", ">=8.0.0,<10.1.0"),
        ("tqdm", ""),
        ("pyyaml", ">=5.4.0,<7.0.0"),
        ("jieba", "")
    ]
    
    # 可选包（如果安装失败不影响核心功能）
    optional_packages = [
        ("speechrecognition", ">=3.8.0"),
        ("pyaudio", ">=0.2.11"),
        ("seaborn", ">=0.11.0,<0.13.0"),
        ("scipy", ">=1.7.0,<1.12.0")
    ]
    
    success_count = 0
    total_count = len(core_packages)
    
    # 先尝试不使用镜像源
    use_mirror = False
    
    for package_name, version_spec in core_packages:
        if install_package(package_name, version_spec, use_mirror):
            success_count += 1
        else:
            # 如果失败，尝试使用镜像源
            print(f"尝试使用镜像源安装 {package_name}...")
            if install_package(package_name, version_spec, True):
                success_count += 1
                use_mirror = True  # 后续包都使用镜像源
    
    print(f"\n核心包安装结果: {success_count}/{total_count}")
    
    # 安装可选包
    print("\n安装可选包...")
    optional_success = 0
    
    for package_name, version_spec in optional_packages:
        if install_package(package_name, version_spec, use_mirror):
            optional_success += 1
        else:
            print(f"可选包 {package_name} 安装失败，跳过...")
    
    print(f"可选包安装结果: {optional_success}/{len(optional_packages)}")
    
    return success_count >= len(core_packages) * 0.8  # 80%的核心包安装成功即可


def check_installation():
    """检查安装结果"""
    print("\n检查安装结果...")
    
    test_imports = [
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("torch", "PyTorch"),
        ("ultralytics", "Ultralytics"),
        ("sklearn", "Scikit-learn"),
        ("matplotlib", "Matplotlib"),
        ("pandas", "Pandas"),
        ("PIL", "Pillow"),
        ("tqdm", "tqdm"),
        ("yaml", "PyYAML"),
        ("jieba", "jieba")
    ]
    
    success_count = 0
    
    for module_name, display_name in test_imports:
        try:
            __import__(module_name)
            print(f"✓ {display_name}")
            success_count += 1
        except ImportError:
            print(f"✗ {display_name}")
    
    # 检查可选包
    optional_imports = [
        ("speech_recognition", "SpeechRecognition"),
        ("pyaudio", "PyAudio"),
        ("seaborn", "Seaborn"),
        ("scipy", "SciPy")
    ]
    
    print("\n可选包检查:")
    for module_name, display_name in optional_imports:
        try:
            __import__(module_name)
            print(f"✓ {display_name}")
        except ImportError:
            print(f"✗ {display_name} (可选)")
    
    print(f"\n核心包安装成功率: {success_count}/{len(test_imports)}")
    return success_count >= len(test_imports) * 0.8


def install_pytorch_separately():
    """单独安装PyTorch（处理特殊情况）"""
    print("尝试单独安装PyTorch...")
    
    system = platform.system().lower()
    machine = platform.machine().lower()
    
    # 根据系统选择合适的PyTorch版本
    if system == "darwin" and "arm" in machine:
        # Apple Silicon Mac
        cmd = [sys.executable, "-m", "pip", "install", "torch", "torchvision", "--index-url", "https://download.pytorch.org/whl/cpu"]
    elif system == "linux":
        # Linux
        cmd = [sys.executable, "-m", "pip", "install", "torch", "torchvision", "--index-url", "https://download.pytorch.org/whl/cpu"]
    else:
        # Windows 或其他
        cmd = [sys.executable, "-m", "pip", "install", "torch", "torchvision"]
    
    try:
        subprocess.check_call(cmd)
        print("PyTorch安装成功")
        return True
    except subprocess.CalledProcessError:
        print("PyTorch安装失败")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("工人操作视频打分工具 - 依赖安装脚本")
    print("=" * 60)
    
    print(f"Python版本: {get_python_version()}")
    print(f"操作系统: {platform.system()} {platform.machine()}")
    
    # 升级pip
    print("\n升级pip...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("pip升级成功")
    except subprocess.CalledProcessError:
        print("pip升级失败，继续安装...")
    
    # 安装核心包
    if not install_core_packages():
        print("\n核心包安装失败，尝试其他方法...")
        
        # 尝试单独安装PyTorch
        if not install_pytorch_separately():
            print("PyTorch安装失败，请手动安装")
        
        # 尝试安装最基础的包
        basic_packages = ["numpy", "opencv-python", "ultralytics"]
        print(f"\n尝试安装基础包: {', '.join(basic_packages)}")
        
        for package in basic_packages:
            install_package(package, "", True)
    
    # 检查安装结果
    if check_installation():
        print("\n🎉 依赖安装成功！")
        print("\n下一步:")
        print("  python src/main.py --mode demo")
        print("  python test_system.py")
    else:
        print("\n⚠️ 部分依赖安装失败")
        print("\n手动安装建议:")
        print("  pip install numpy opencv-python ultralytics")
        print("  pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu")
        print("\n或使用conda:")
        print("  conda install pytorch torchvision opencv numpy")


if __name__ == "__main__":
    main()
