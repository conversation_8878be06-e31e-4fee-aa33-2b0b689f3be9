"""
工人操作视频打分工具演示脚本
展示系统的主要功能
"""

import os
import sys
import time
import json

# 添加src目录到路径
sys.path.append('src')

from pose_detection import PoseDetector
from action_similarity import ActionSimilarityCalculator
from speech_recognition import TextSimilarityCalculator
from video_capture import VideoCapture


def demo_pose_detection():
    """演示姿态检测功能"""
    print("\n" + "="*50)
    print("演示1: 姿态检测功能")
    print("="*50)
    
    try:
        detector = PoseDetector()
        print("✓ YOLOv8 pose模型加载成功")
        
        # 测试摄像头
        capture = VideoCapture()
        if capture.initialize_camera():
            print("✓ 摄像头初始化成功")
            
            print("\n按任意键开始姿态检测演示（按'q'退出）...")
            input()
            
            capture.start_streaming()
            
            frame_count = 0
            start_time = time.time()
            
            while frame_count < 100:  # 演示100帧
                frame = capture.get_current_frame()
                if frame is not None:
                    # 检测姿态
                    poses = detector.detect_pose(frame)
                    
                    if poses:
                        # 绘制姿态
                        result_img = detector.draw_pose(frame, poses)
                        
                        # 显示帧率信息
                        fps = frame_count / (time.time() - start_time)
                        cv2.putText(result_img, f"FPS: {fps:.1f}", (10, 30), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                        
                        cv2.imshow('姿态检测演示', result_img)
                        
                        if cv2.waitKey(1) & 0xFF == ord('q'):
                            break
                    
                    frame_count += 1
                
                time.sleep(0.033)  # 约30fps
            
            capture.release()
            cv2.destroyAllWindows()
            print("✓ 姿态检测演示完成")
        else:
            print("✗ 摄像头初始化失败")
    
    except Exception as e:
        print(f"✗ 姿态检测演示失败: {e}")


def demo_action_similarity():
    """演示动作相似度计算"""
    print("\n" + "="*50)
    print("演示2: 动作相似度计算")
    print("="*50)
    
    try:
        calculator = ActionSimilarityCalculator()
        print("✓ 动作相似度计算器初始化成功")
        
        # 创建测试数据
        standard_pose = {
            'left_shoulder': (100, 200),
            'right_shoulder': (200, 200),
            'left_elbow': (80, 250),
            'right_elbow': (220, 250),
            'left_wrist': (60, 300),
            'right_wrist': (240, 300),
            'left_hip': (120, 350),
            'right_hip': (180, 350)
        }
        
        # 测试不同相似度的姿态
        test_cases = [
            {
                'name': '完全相同',
                'pose': standard_pose.copy()
            },
            {
                'name': '轻微差异',
                'pose': {k: (v[0] + 5, v[1] + 5) for k, v in standard_pose.items()}
            },
            {
                'name': '中等差异',
                'pose': {k: (v[0] + 20, v[1] + 20) for k, v in standard_pose.items()}
            },
            {
                'name': '较大差异',
                'pose': {k: (v[0] + 50, v[1] + 50) for k, v in standard_pose.items()}
            }
        ]
        
        print("\n姿态相似度测试结果:")
        for test_case in test_cases:
            similarity = calculator.calculate_pose_similarity(
                standard_pose, test_case['pose']
            )
            print(f"  {test_case['name']}: {similarity:.3f}")
        
        # 测试序列相似度
        standard_sequence = [standard_pose] * 10
        test_sequence = [test_cases[1]['pose']] * 10
        
        seq_similarity = calculator.calculate_sequence_similarity(
            standard_sequence, test_sequence
        )
        print(f"\n序列相似度测试: {seq_similarity:.3f}")
        
        # 测试动作质量分析
        analysis = calculator.analyze_action_quality(
            standard_sequence, test_sequence
        )
        
        print(f"\n动作质量分析:")
        print(f"  整体评分: {analysis['overall_score']:.3f}")
        print(f"  评分等级: {analysis['score_grade']}")
        print(f"  改进建议: {', '.join(analysis['recommendations'][:2])}")
        
        print("✓ 动作相似度计算演示完成")
    
    except Exception as e:
        print(f"✗ 动作相似度计算演示失败: {e}")


def demo_text_similarity():
    """演示文本相似度计算"""
    print("\n" + "="*50)
    print("演示3: 文本相似度计算")
    print("="*50)
    
    try:
        calculator = TextSimilarityCalculator()
        print("✓ 文本相似度计算器初始化成功")
        
        standard_text = "设备运行正常，所有指标都在标准范围内，操作流程按照标准执行，没有发现异常情况。"
        
        test_texts = [
            "设备运行正常，所有指标都在标准范围内，操作流程按照标准执行，没有发现异常情况。",
            "设备状态良好，各项指标正常，按标准流程操作，未发现异常。",
            "设备工作正常，指标在正常范围，操作符合标准，无异常发现。",
            "机器运转良好，数据显示正常，按规程操作，一切正常。",
            "今天天气很好，阳光明媚，适合外出游玩。"
        ]
        
        keywords = ["设备", "正常", "指标", "标准", "操作", "异常"]
        
        print("\n文本相似度测试结果:")
        for i, test_text in enumerate(test_texts):
            simple_sim = calculator.calculate_similarity_simple(standard_text, test_text)
            tfidf_sim = calculator.calculate_similarity_tfidf(standard_text, test_text)
            
            print(f"  测试文本{i+1}:")
            print(f"    简单相似度: {simple_sim:.3f}")
            print(f"    TF-IDF相似度: {tfidf_sim:.3f}")
        
        # 测试关键词相似度
        keyword_scores = calculator.calculate_keyword_similarity(
            standard_text, test_texts[1], keywords
        )
        
        print(f"\n关键词匹配测试:")
        for keyword, score in keyword_scores.items():
            print(f"  {keyword}: {score}")
        
        # 测试文本质量分析
        analysis = calculator.analyze_text_quality(
            standard_text, test_texts[1], keywords
        )
        
        print(f"\n文本质量分析:")
        print(f"  整体相似度: {analysis['overall_similarity']:.3f}")
        print(f"  评分等级: {analysis['score_grade']}")
        print(f"  关键词覆盖率: {analysis['keyword_analysis']['keyword_coverage']:.3f}")
        
        print("✓ 文本相似度计算演示完成")
    
    except Exception as e:
        print(f"✗ 文本相似度计算演示失败: {e}")


def demo_training_data():
    """演示训练数据创建"""
    print("\n" + "="*50)
    print("演示4: 训练数据创建")
    print("="*50)
    
    try:
        from data.training_data.create_training_data import save_training_data
        
        print("正在创建示例训练数据...")
        save_training_data()
        print("✓ 示例训练数据创建完成")
        
        # 检查创建的文件
        data_dir = "data/standard_actions"
        if os.path.exists(data_dir):
            actions = os.listdir(data_dir)
            print(f"✓ 创建了 {len(actions)} 个标准动作:")
            for action in actions:
                print(f"    - {action}")
        
        sample_dir = "data/training_data/sample_data"
        if os.path.exists(sample_dir):
            samples = [f for f in os.listdir(sample_dir) if f.endswith('.txt')]
            print(f"✓ 创建了 {len(samples)} 个示例报告")
    
    except Exception as e:
        print(f"✗ 训练数据创建演示失败: {e}")


def demo_full_system():
    """演示完整系统功能"""
    print("\n" + "="*50)
    print("演示5: 完整系统功能")
    print("="*50)
    
    try:
        from main import WorkerEvaluationSystem
        
        system = WorkerEvaluationSystem()
        print("✓ 工人评估系统初始化成功")
        
        print(f"✓ 加载了 {len(system.standard_actions)} 个标准动作")
        print(f"✓ 加载了 {len(system.standard_reports)} 个标准报告")
        
        if system.standard_actions:
            action_name = list(system.standard_actions.keys())[0]
            print(f"\n可以使用以下命令进行动作评估:")
            print(f"  python src/main.py --mode action --action {action_name}")
        
        if system.standard_reports:
            report_name = list(system.standard_reports.keys())[0]
            print(f"\n可以使用以下命令进行语音评估:")
            print(f"  python src/main.py --mode voice --report {report_name}")
        
        print(f"\n或者使用交互式模式:")
        print(f"  python src/main.py")
        
        print("✓ 完整系统功能演示完成")
    
    except Exception as e:
        print(f"✗ 完整系统功能演示失败: {e}")


def main():
    """主演示函数"""
    print("工人操作视频打分工具 - 功能演示")
    print("本演示将展示系统的主要功能模块")
    
    demos = [
        ("姿态检测", demo_pose_detection),
        ("动作相似度计算", demo_action_similarity),
        ("文本相似度计算", demo_text_similarity),
        ("训练数据创建", demo_training_data),
        ("完整系统功能", demo_full_system)
    ]
    
    print(f"\n可用演示:")
    for i, (name, _) in enumerate(demos, 1):
        print(f"  {i}. {name}")
    print(f"  0. 运行所有演示")
    
    try:
        choice = input("\n请选择要运行的演示 (0-5): ").strip()
        
        if choice == "0":
            # 运行所有演示
            for name, demo_func in demos:
                print(f"\n正在运行: {name}")
                demo_func()
                time.sleep(1)
        elif choice in ["1", "2", "3", "4", "5"]:
            idx = int(choice) - 1
            name, demo_func = demos[idx]
            print(f"\n正在运行: {name}")
            demo_func()
        else:
            print("无效选择")
            return
    
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")
    
    print("\n演示结束")


if __name__ == "__main__":
    # 导入必要的库
    try:
        import cv2
    except ImportError:
        print("错误: 请先安装依赖包")
        print("运行: pip install -r requirements.txt")
        sys.exit(1)
    
    main()
