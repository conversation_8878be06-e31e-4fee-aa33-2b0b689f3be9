"""
动作相似度计算模块
实现标准动作与实际动作的相似度计算算法
"""

import numpy as np
import json
from typing import List, Dict, Tuple, Optional
from sklearn.metrics.pairwise import cosine_similarity
from scipy.spatial.distance import euclidean
from scipy.optimize import linear_sum_assignment
import cv2


class ActionSimilarityCalculator:
    """动作相似度计算器"""
    
    def __init__(self):
        """初始化相似度计算器"""
        # 关节点权重（重要关节点权重更高）
        self.joint_weights = {
            'left_shoulder': 1.0, 'right_shoulder': 1.0,
            'left_elbow': 0.8, 'right_elbow': 0.8,
            'left_wrist': 1.2, 'right_wrist': 1.2,  # 手腕动作很重要
            'left_hip': 0.6, 'right_hip': 0.6,
            'left_knee': 0.7, 'right_knee': 0.7,
            'left_ankle': 0.5, 'right_ankle': 0.5
        }
        
    def normalize_pose(self, pose_joints: Dict[str, Tu<PERSON>[float, float]], 
                      reference_joint: str = 'left_shoulder') -> Dict[str, Tuple[float, float]]:
        """
        标准化姿态，以参考关节点为原点
        
        Args:
            pose_joints: 关节点坐标字典
            reference_joint: 参考关节点名称
            
        Returns:
            标准化后的关节点坐标
        """
        if reference_joint not in pose_joints:
            # 如果参考关节点不存在，寻找替代点
            available_joints = list(pose_joints.keys())
            if not available_joints:
                return {}
            reference_joint = available_joints[0]
        
        ref_x, ref_y = pose_joints[reference_joint]
        normalized_joints = {}
        
        for joint, (x, y) in pose_joints.items():
            normalized_joints[joint] = (x - ref_x, y - ref_y)
            
        return normalized_joints
    
    def calculate_pose_similarity(self, pose1: Dict[str, Tuple[float, float]], 
                                pose2: Dict[str, Tuple[float, float]]) -> float:
        """
        计算两个姿态的相似度
        
        Args:
            pose1: 第一个姿态的关节点坐标
            pose2: 第二个姿态的关节点坐标
            
        Returns:
            相似度分数 (0-1)
        """
        # 标准化姿态
        norm_pose1 = self.normalize_pose(pose1)
        norm_pose2 = self.normalize_pose(pose2)
        
        # 找到共同的关节点
        common_joints = set(norm_pose1.keys()) & set(norm_pose2.keys())
        
        if len(common_joints) < 3:  # 至少需要3个关节点
            return 0.0
        
        total_similarity = 0.0
        total_weight = 0.0
        
        for joint in common_joints:
            # 计算欧几里得距离
            distance = euclidean(norm_pose1[joint], norm_pose2[joint])
            
            # 转换为相似度（距离越小，相似度越高）
            # 使用指数衰减函数
            similarity = np.exp(-distance / 100.0)  # 100是缩放因子
            
            # 应用权重
            weight = self.joint_weights.get(joint, 0.5)
            total_similarity += similarity * weight
            total_weight += weight
        
        return total_similarity / total_weight if total_weight > 0 else 0.0
    
    def calculate_sequence_similarity(self, sequence1: List[Dict[str, Tuple[float, float]]], 
                                    sequence2: List[Dict[str, Tuple[float, float]]],
                                    use_dtw: bool = True) -> float:
        """
        计算两个动作序列的相似度
        
        Args:
            sequence1: 标准动作序列
            sequence2: 实际动作序列
            use_dtw: 是否使用动态时间规整
            
        Returns:
            序列相似度分数 (0-1)
        """
        if not sequence1 or not sequence2:
            return 0.0
        
        if use_dtw:
            return self._dtw_similarity(sequence1, sequence2)
        else:
            return self._linear_similarity(sequence1, sequence2)
    
    def _dtw_similarity(self, seq1: List[Dict], seq2: List[Dict]) -> float:
        """
        使用动态时间规整计算序列相似度
        
        Args:
            seq1: 序列1
            seq2: 序列2
            
        Returns:
            DTW相似度分数
        """
        m, n = len(seq1), len(seq2)
        
        # 创建距离矩阵
        distance_matrix = np.zeros((m, n))
        for i in range(m):
            for j in range(n):
                distance_matrix[i, j] = 1.0 - self.calculate_pose_similarity(seq1[i], seq2[j])
        
        # DTW动态规划
        dtw_matrix = np.full((m + 1, n + 1), np.inf)
        dtw_matrix[0, 0] = 0
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                cost = distance_matrix[i-1, j-1]
                dtw_matrix[i, j] = cost + min(
                    dtw_matrix[i-1, j],      # 插入
                    dtw_matrix[i, j-1],      # 删除
                    dtw_matrix[i-1, j-1]     # 匹配
                )
        
        # 标准化DTW距离
        dtw_distance = dtw_matrix[m, n] / (m + n)
        return max(0.0, 1.0 - dtw_distance)
    
    def _linear_similarity(self, seq1: List[Dict], seq2: List[Dict]) -> float:
        """
        线性对齐计算序列相似度
        
        Args:
            seq1: 序列1
            seq2: 序列2
            
        Returns:
            线性相似度分数
        """
        # 将序列长度对齐到较短的序列
        min_len = min(len(seq1), len(seq2))
        
        # 重采样到相同长度
        indices1 = np.linspace(0, len(seq1) - 1, min_len, dtype=int)
        indices2 = np.linspace(0, len(seq2) - 1, min_len, dtype=int)
        
        similarities = []
        for i1, i2 in zip(indices1, indices2):
            sim = self.calculate_pose_similarity(seq1[i1], seq2[i2])
            similarities.append(sim)
        
        return np.mean(similarities)
    
    def analyze_action_quality(self, standard_sequence: List[Dict], 
                             actual_sequence: List[Dict]) -> Dict:
        """
        分析动作质量，提供详细的评分和建议
        
        Args:
            standard_sequence: 标准动作序列
            actual_sequence: 实际动作序列
            
        Returns:
            分析结果字典
        """
        overall_similarity = self.calculate_sequence_similarity(
            standard_sequence, actual_sequence
        )
        
        # 分段分析
        segment_scores = []
        num_segments = min(5, min(len(standard_sequence), len(actual_sequence)))
        
        if num_segments > 0:
            std_segment_size = len(standard_sequence) // num_segments
            act_segment_size = len(actual_sequence) // num_segments
            
            for i in range(num_segments):
                std_start = i * std_segment_size
                std_end = (i + 1) * std_segment_size if i < num_segments - 1 else len(standard_sequence)
                act_start = i * act_segment_size
                act_end = (i + 1) * act_segment_size if i < num_segments - 1 else len(actual_sequence)
                
                segment_sim = self.calculate_sequence_similarity(
                    standard_sequence[std_start:std_end],
                    actual_sequence[act_start:act_end]
                )
                segment_scores.append(segment_sim)
        
        # 关键关节点分析
        joint_analysis = self._analyze_joint_performance(standard_sequence, actual_sequence)
        
        # 生成评分等级
        score_grade = self._get_score_grade(overall_similarity)
        
        return {
            'overall_score': overall_similarity,
            'score_grade': score_grade,
            'segment_scores': segment_scores,
            'joint_analysis': joint_analysis,
            'recommendations': self._generate_recommendations(joint_analysis, overall_similarity)
        }
    
    def _analyze_joint_performance(self, standard_seq: List[Dict], 
                                 actual_seq: List[Dict]) -> Dict:
        """分析各关节点的表现"""
        joint_scores = {}
        
        if not standard_seq or not actual_seq:
            return joint_scores
        
        # 获取所有可能的关节点
        all_joints = set()
        for pose in standard_seq + actual_seq:
            all_joints.update(pose.keys())
        
        for joint in all_joints:
            joint_similarities = []
            
            # 计算每帧的关节点相似度
            min_len = min(len(standard_seq), len(actual_seq))
            for i in range(min_len):
                if joint in standard_seq[i] and joint in actual_seq[i]:
                    # 单关节点相似度计算
                    std_pos = standard_seq[i][joint]
                    act_pos = actual_seq[i][joint]
                    distance = euclidean(std_pos, act_pos)
                    similarity = np.exp(-distance / 50.0)
                    joint_similarities.append(similarity)
            
            if joint_similarities:
                joint_scores[joint] = {
                    'average_score': np.mean(joint_similarities),
                    'consistency': 1.0 - np.std(joint_similarities),
                    'min_score': np.min(joint_similarities),
                    'max_score': np.max(joint_similarities)
                }
        
        return joint_scores
    
    def _get_score_grade(self, score: float) -> str:
        """根据分数获取等级"""
        if score >= 0.9:
            return "优秀"
        elif score >= 0.8:
            return "良好"
        elif score >= 0.7:
            return "中等"
        elif score >= 0.6:
            return "及格"
        else:
            return "需要改进"
    
    def _generate_recommendations(self, joint_analysis: Dict, overall_score: float) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if overall_score < 0.7:
            recommendations.append("整体动作需要更多练习，建议重复观看标准动作视频")
        
        # 分析表现较差的关节点
        poor_joints = []
        for joint, analysis in joint_analysis.items():
            if analysis['average_score'] < 0.6:
                poor_joints.append(joint)
        
        if poor_joints:
            recommendations.append(f"需要重点关注以下关节点的动作: {', '.join(poor_joints)}")
        
        # 分析一致性较差的关节点
        inconsistent_joints = []
        for joint, analysis in joint_analysis.items():
            if analysis['consistency'] < 0.7:
                inconsistent_joints.append(joint)
        
        if inconsistent_joints:
            recommendations.append(f"以下关节点动作不够稳定: {', '.join(inconsistent_joints)}")
        
        if not recommendations:
            recommendations.append("动作表现良好，继续保持！")
        
        return recommendations


def save_standard_action(action_sequence: List[Dict], filename: str):
    """保存标准动作序列到文件"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(action_sequence, f, ensure_ascii=False, indent=2)


def load_standard_action(filename: str) -> List[Dict]:
    """从文件加载标准动作序列"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"标准动作文件 {filename} 不存在")
        return []
    except json.JSONDecodeError:
        print(f"标准动作文件 {filename} 格式错误")
        return []


if __name__ == "__main__":
    # 测试代码
    calculator = ActionSimilarityCalculator()
    
    # 创建测试数据
    test_pose1 = {
        'left_shoulder': (100, 200),
        'right_shoulder': (200, 200),
        'left_elbow': (80, 250),
        'right_elbow': (220, 250),
        'left_wrist': (60, 300),
        'right_wrist': (240, 300)
    }
    
    test_pose2 = {
        'left_shoulder': (105, 205),
        'right_shoulder': (195, 205),
        'left_elbow': (85, 255),
        'right_elbow': (215, 255),
        'left_wrist': (65, 305),
        'right_wrist': (235, 305)
    }
    
    similarity = calculator.calculate_pose_similarity(test_pose1, test_pose2)
    print(f"姿态相似度: {similarity:.3f}")
    
    # 测试序列相似度
    sequence1 = [test_pose1] * 10
    sequence2 = [test_pose2] * 10
    
    seq_similarity = calculator.calculate_sequence_similarity(sequence1, sequence2)
    print(f"序列相似度: {seq_similarity:.3f}")
    
    # 测试动作质量分析
    analysis = calculator.analyze_action_quality(sequence1, sequence2)
    print(f"动作质量分析: {analysis}")
