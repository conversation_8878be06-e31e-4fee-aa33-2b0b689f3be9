"""
姿态检测模块
基于YOLOv8 pose实现人体姿态检测，提取关键点坐标
"""

import cv2
import numpy as np
from ultralytics import YOLO
import torch
from typing import List, Tuple, Optional, Dict


class PoseDetector:
    """YOLOv8姿态检测器"""
    
    def __init__(self, model_path: str = "yolov8n-pose.pt"):
        """
        初始化姿态检测器
        
        Args:
            model_path: YOLOv8 pose模型路径
        """
        self.model = YOLO(model_path)
        self.keypoint_names = [
            'nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear',
            'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
            'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
            'left_knee', 'right_knee', 'left_ankle', 'right_ankle'
        ]
        
    def detect_pose(self, image: np.ndarray, conf_threshold: float = 0.5) -> List[Dict]:
        """
        检测图像中的人体姿态
        
        Args:
            image: 输入图像
            conf_threshold: 置信度阈值
            
        Returns:
            检测结果列表，每个元素包含关键点坐标和置信度
        """
        results = self.model(image, conf=conf_threshold)
        poses = []
        
        for result in results:
            if result.keypoints is not None:
                keypoints = result.keypoints.data.cpu().numpy()
                boxes = result.boxes.data.cpu().numpy() if result.boxes is not None else None
                
                for i, kpts in enumerate(keypoints):
                    pose_data = {
                        'keypoints': kpts,  # shape: (17, 3) - x, y, confidence
                        'bbox': boxes[i] if boxes is not None else None,
                        'keypoint_dict': self._keypoints_to_dict(kpts)
                    }
                    poses.append(pose_data)
                    
        return poses
    
    def _keypoints_to_dict(self, keypoints: np.ndarray) -> Dict[str, Tuple[float, float, float]]:
        """
        将关键点数组转换为字典格式
        
        Args:
            keypoints: 关键点数组 (17, 3)
            
        Returns:
            关键点字典 {name: (x, y, confidence)}
        """
        keypoint_dict = {}
        for i, name in enumerate(self.keypoint_names):
            if i < len(keypoints):
                x, y, conf = keypoints[i]
                keypoint_dict[name] = (float(x), float(y), float(conf))
            else:
                keypoint_dict[name] = (0.0, 0.0, 0.0)
        return keypoint_dict
    
    def draw_pose(self, image: np.ndarray, poses: List[Dict], 
                  draw_skeleton: bool = True, draw_keypoints: bool = True) -> np.ndarray:
        """
        在图像上绘制姿态关键点和骨架
        
        Args:
            image: 输入图像
            poses: 姿态检测结果
            draw_skeleton: 是否绘制骨架
            draw_keypoints: 是否绘制关键点
            
        Returns:
            绘制后的图像
        """
        img_copy = image.copy()
        
        # 定义骨架连接
        skeleton = [
            (0, 1), (0, 2), (1, 3), (2, 4),  # 头部
            (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),  # 上身
            (5, 11), (6, 12), (11, 12),  # 躯干
            (11, 13), (13, 15), (12, 14), (14, 16)  # 下身
        ]
        
        for pose in poses:
            keypoints = pose['keypoints']
            
            # 绘制关键点
            if draw_keypoints:
                for i, (x, y, conf) in enumerate(keypoints):
                    if conf > 0.5:  # 只绘制置信度高的关键点
                        cv2.circle(img_copy, (int(x), int(y)), 3, (0, 255, 0), -1)
                        cv2.putText(img_copy, str(i), (int(x), int(y-5)), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
            
            # 绘制骨架
            if draw_skeleton:
                for start_idx, end_idx in skeleton:
                    if (start_idx < len(keypoints) and end_idx < len(keypoints)):
                        start_point = keypoints[start_idx]
                        end_point = keypoints[end_idx]
                        
                        if start_point[2] > 0.5 and end_point[2] > 0.5:
                            cv2.line(img_copy, 
                                   (int(start_point[0]), int(start_point[1])),
                                   (int(end_point[0]), int(end_point[1])),
                                   (255, 0, 0), 2)
        
        return img_copy
    
    def extract_key_joints(self, pose_data: Dict) -> Dict[str, Tuple[float, float]]:
        """
        提取关键关节点用于动作分析
        
        Args:
            pose_data: 姿态数据
            
        Returns:
            关键关节点坐标字典
        """
        keypoint_dict = pose_data['keypoint_dict']
        key_joints = {}
        
        # 提取主要关节点（去除置信度，只保留坐标）
        important_joints = [
            'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
            'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
            'left_knee', 'right_knee', 'left_ankle', 'right_ankle'
        ]
        
        for joint in important_joints:
            if joint in keypoint_dict:
                x, y, conf = keypoint_dict[joint]
                if conf > 0.3:  # 只保留置信度较高的关节点
                    key_joints[joint] = (x, y)
                    
        return key_joints


def test_pose_detection():
    """测试姿态检测功能"""
    detector = PoseDetector()
    
    # 测试摄像头
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("无法打开摄像头")
        return
    
    print("按 'q' 退出测试")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
            
        # 检测姿态
        poses = detector.detect_pose(frame)
        
        # 绘制结果
        result_img = detector.draw_pose(frame, poses)
        
        # 显示关键关节点信息
        if poses:
            key_joints = detector.extract_key_joints(poses[0])
            y_offset = 30
            for joint, (x, y) in key_joints.items():
                cv2.putText(result_img, f"{joint}: ({x:.1f}, {y:.1f})", 
                          (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                y_offset += 20
        
        cv2.imshow('Pose Detection Test', result_img)
        
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()


if __name__ == "__main__":
    test_pose_detection()
