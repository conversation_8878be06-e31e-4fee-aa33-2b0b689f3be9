"""
摄像头视频采集模块
实现实时摄像头视频流获取和处理
"""

import cv2
import numpy as np
import threading
import time
from typing import List, Dict, Optional, Callable, Tuple
import queue
from datetime import datetime
import os


class VideoCapture:
    """视频采集器"""
    
    def __init__(self, camera_id: int = 0, fps: int = 30):
        """
        初始化视频采集器
        
        Args:
            camera_id: 摄像头ID
            fps: 帧率
        """
        self.camera_id = camera_id
        self.fps = fps
        self.cap = None
        self.is_recording = False
        self.is_streaming = False
        self.frame_queue = queue.Queue(maxsize=30)
        self.current_frame = None
        self.frame_count = 0
        self.start_time = None
        
        # 录制相关
        self.video_writer = None
        self.recording_filename = None
        
        # 回调函数
        self.frame_callback = None
        
    def initialize_camera(self) -> bool:
        """
        初始化摄像头
        
        Returns:
            是否初始化成功
        """
        try:
            self.cap = cv2.VideoCapture(self.camera_id)
            if not self.cap.isOpened():
                print(f"无法打开摄像头 {self.camera_id}")
                return False
            
            # 设置摄像头参数
            self.cap.set(cv2.CAP_PROP_FPS, self.fps)
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            
            # 获取实际参数
            actual_fps = self.cap.get(cv2.CAP_PROP_FPS)
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            print(f"摄像头初始化成功: {width}x{height} @ {actual_fps}fps")
            return True
            
        except Exception as e:
            print(f"摄像头初始化失败: {e}")
            return False
    
    def start_streaming(self, frame_callback: Optional[Callable] = None):
        """
        开始视频流
        
        Args:
            frame_callback: 帧处理回调函数
        """
        if not self.cap or not self.cap.isOpened():
            if not self.initialize_camera():
                return False
        
        self.frame_callback = frame_callback
        self.is_streaming = True
        self.start_time = time.time()
        self.frame_count = 0
        
        # 启动采集线程
        self.capture_thread = threading.Thread(target=self._capture_loop)
        self.capture_thread.daemon = True
        self.capture_thread.start()
        
        return True
    
    def stop_streaming(self):
        """停止视频流"""
        self.is_streaming = False
        if hasattr(self, 'capture_thread'):
            self.capture_thread.join(timeout=1.0)
    
    def _capture_loop(self):
        """视频采集循环"""
        while self.is_streaming and self.cap and self.cap.isOpened():
            ret, frame = self.cap.read()
            if not ret:
                print("无法读取摄像头帧")
                break
            
            self.current_frame = frame.copy()
            self.frame_count += 1
            
            # 添加到队列
            if not self.frame_queue.full():
                self.frame_queue.put(frame.copy())
            else:
                # 队列满时，移除最老的帧
                try:
                    self.frame_queue.get_nowait()
                    self.frame_queue.put(frame.copy())
                except queue.Empty:
                    pass
            
            # 录制视频
            if self.is_recording and self.video_writer:
                self.video_writer.write(frame)
            
            # 调用回调函数
            if self.frame_callback:
                try:
                    self.frame_callback(frame.copy())
                except Exception as e:
                    print(f"帧回调函数错误: {e}")
            
            # 控制帧率
            time.sleep(1.0 / self.fps)
    
    def get_current_frame(self) -> Optional[np.ndarray]:
        """
        获取当前帧
        
        Returns:
            当前帧图像
        """
        return self.current_frame.copy() if self.current_frame is not None else None
    
    def get_frame_from_queue(self, timeout: float = 1.0) -> Optional[np.ndarray]:
        """
        从队列获取帧
        
        Args:
            timeout: 超时时间
            
        Returns:
            帧图像
        """
        try:
            return self.frame_queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def start_recording(self, filename: Optional[str] = None) -> bool:
        """
        开始录制视频
        
        Args:
            filename: 录制文件名
            
        Returns:
            是否开始录制成功
        """
        if not self.is_streaming:
            print("请先开始视频流")
            return False
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"recording_{timestamp}.mp4"
        
        self.recording_filename = filename
        
        # 获取视频参数
        width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        self.video_writer = cv2.VideoWriter(filename, fourcc, self.fps, (width, height))
        
        if not self.video_writer.isOpened():
            print(f"无法创建视频文件: {filename}")
            return False
        
        self.is_recording = True
        print(f"开始录制视频: {filename}")
        return True
    
    def stop_recording(self):
        """停止录制视频"""
        if self.is_recording:
            self.is_recording = False
            if self.video_writer:
                self.video_writer.release()
                self.video_writer = None
            print(f"录制完成: {self.recording_filename}")
    
    def capture_frames(self, duration: float, 
                      frame_callback: Optional[Callable] = None) -> List[np.ndarray]:
        """
        采集指定时长的帧
        
        Args:
            duration: 采集时长（秒）
            frame_callback: 帧处理回调
            
        Returns:
            帧列表
        """
        frames = []
        start_time = time.time()
        
        if not self.is_streaming:
            self.start_streaming()
        
        while time.time() - start_time < duration:
            frame = self.get_frame_from_queue(timeout=0.1)
            if frame is not None:
                frames.append(frame.copy())
                if frame_callback:
                    frame_callback(frame)
        
        return frames
    
    def get_fps_info(self) -> Dict[str, float]:
        """
        获取帧率信息
        
        Returns:
            帧率信息字典
        """
        if self.start_time is None:
            return {'actual_fps': 0, 'target_fps': self.fps}
        
        elapsed_time = time.time() - self.start_time
        actual_fps = self.frame_count / elapsed_time if elapsed_time > 0 else 0
        
        return {
            'actual_fps': actual_fps,
            'target_fps': self.fps,
            'frame_count': self.frame_count,
            'elapsed_time': elapsed_time
        }
    
    def release(self):
        """释放资源"""
        self.stop_streaming()
        self.stop_recording()
        
        if self.cap:
            self.cap.release()
            self.cap = None
        
        cv2.destroyAllWindows()


class ActionRecorder:
    """动作录制器"""
    
    def __init__(self, video_capture: VideoCapture):
        """
        初始化动作录制器
        
        Args:
            video_capture: 视频采集器
        """
        self.video_capture = video_capture
        self.recorded_frames = []
        self.is_recording_action = False
        self.action_start_time = None
    
    def start_action_recording(self):
        """开始动作录制"""
        self.recorded_frames = []
        self.is_recording_action = True
        self.action_start_time = time.time()
        print("开始录制动作...")
    
    def stop_action_recording(self) -> List[np.ndarray]:
        """
        停止动作录制
        
        Returns:
            录制的帧列表
        """
        self.is_recording_action = False
        duration = time.time() - self.action_start_time if self.action_start_time else 0
        print(f"动作录制完成，时长: {duration:.2f}秒，帧数: {len(self.recorded_frames)}")
        return self.recorded_frames.copy()
    
    def record_frame(self, frame: np.ndarray):
        """
        录制帧
        
        Args:
            frame: 输入帧
        """
        if self.is_recording_action:
            self.recorded_frames.append(frame.copy())
    
    def record_action_sequence(self, duration: float) -> List[np.ndarray]:
        """
        录制指定时长的动作序列
        
        Args:
            duration: 录制时长
            
        Returns:
            动作帧序列
        """
        self.start_action_recording()
        
        # 设置帧回调
        original_callback = self.video_capture.frame_callback
        self.video_capture.frame_callback = self.record_frame
        
        # 等待录制完成
        time.sleep(duration)
        
        # 恢复原回调
        self.video_capture.frame_callback = original_callback
        
        return self.stop_action_recording()
    
    def save_action_frames(self, frames: List[np.ndarray], 
                          output_dir: str = "data/recorded_actions"):
        """
        保存动作帧到文件
        
        Args:
            frames: 帧列表
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for i, frame in enumerate(frames):
            filename = os.path.join(output_dir, f"action_{timestamp}_frame_{i:04d}.jpg")
            cv2.imwrite(filename, frame)
        
        print(f"保存了 {len(frames)} 帧到 {output_dir}")


def test_video_capture():
    """测试视频采集功能"""
    capture = VideoCapture()
    
    if not capture.initialize_camera():
        print("摄像头初始化失败")
        return
    
    # 创建动作录制器
    recorder = ActionRecorder(capture)
    
    def frame_processor(frame):
        """帧处理函数"""
        # 在帧上添加时间戳
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        cv2.putText(frame, timestamp, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # 显示帧
        cv2.imshow('Video Capture Test', frame)
        
        # 检查按键
        key = cv2.waitKey(1) & 0xFF
        if key == ord('r'):  # 按'r'开始录制
            if not recorder.is_recording_action:
                recorder.start_action_recording()
        elif key == ord('s'):  # 按's'停止录制
            if recorder.is_recording_action:
                frames = recorder.stop_action_recording()
                recorder.save_action_frames(frames)
        elif key == ord('q'):  # 按'q'退出
            capture.stop_streaming()
    
    # 开始视频流
    capture.start_streaming(frame_processor)
    
    print("视频采集测试开始")
    print("按键说明:")
    print("  'r' - 开始录制动作")
    print("  's' - 停止录制动作")
    print("  'q' - 退出")
    
    try:
        while capture.is_streaming:
            time.sleep(0.1)
    except KeyboardInterrupt:
        print("用户中断")
    
    capture.release()
    print("测试结束")


if __name__ == "__main__":
    test_video_capture()
