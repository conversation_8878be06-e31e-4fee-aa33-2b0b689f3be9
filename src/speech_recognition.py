"""
语音识别和文本相似度模块
实现语音转文字功能和文本相似度计算
"""

import speech_recognition as sr
import pyaudio
import wave
import threading
import time
import difflib
import re
import jieba
from typing import List, Dict, Optional, Tuple
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity


class SpeechRecognizer:
    """语音识别器"""
    
    def __init__(self, language: str = "zh-CN"):
        """
        初始化语音识别器
        
        Args:
            language: 识别语言，默认中文
        """
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        self.language = language
        self.is_recording = False
        self.audio_data = None
        
        # 调整环境噪音
        with self.microphone as source:
            self.recognizer.adjust_for_ambient_noise(source)
    
    def record_audio(self, duration: Optional[float] = None) -> bool:
        """
        录制音频
        
        Args:
            duration: 录制时长（秒），None表示手动停止
            
        Returns:
            是否录制成功
        """
        try:
            with self.microphone as source:
                print("开始录音...")
                if duration:
                    self.audio_data = self.recognizer.listen(source, timeout=1, phrase_time_limit=duration)
                else:
                    self.audio_data = self.recognizer.listen(source, timeout=1)
                print("录音完成")
                return True
        except sr.WaitTimeoutError:
            print("录音超时")
            return False
        except Exception as e:
            print(f"录音错误: {e}")
            return False
    
    def recognize_speech(self, audio_data: Optional[sr.AudioData] = None) -> str:
        """
        识别语音为文字
        
        Args:
            audio_data: 音频数据，None则使用最近录制的音频
            
        Returns:
            识别的文字
        """
        if audio_data is None:
            audio_data = self.audio_data
        
        if audio_data is None:
            return ""
        
        try:
            # 使用Google语音识别
            text = self.recognizer.recognize_google(audio_data, language=self.language)
            return text
        except sr.UnknownValueError:
            print("无法识别语音")
            return ""
        except sr.RequestError as e:
            print(f"语音识别服务错误: {e}")
            # 尝试使用离线识别
            try:
                text = self.recognizer.recognize_sphinx(audio_data, language=self.language)
                return text
            except:
                return ""
    
    def record_and_recognize(self, duration: Optional[float] = None) -> str:
        """
        录制并识别语音
        
        Args:
            duration: 录制时长
            
        Returns:
            识别的文字
        """
        if self.record_audio(duration):
            return self.recognize_speech()
        return ""
    
    def save_audio(self, filename: str, audio_data: Optional[sr.AudioData] = None):
        """
        保存音频到文件
        
        Args:
            filename: 文件名
            audio_data: 音频数据
        """
        if audio_data is None:
            audio_data = self.audio_data
        
        if audio_data is None:
            print("没有音频数据可保存")
            return
        
        with open(filename, "wb") as f:
            f.write(audio_data.get_wav_data())


class TextSimilarityCalculator:
    """文本相似度计算器"""
    
    def __init__(self):
        """初始化文本相似度计算器"""
        self.vectorizer = TfidfVectorizer()
        
    def preprocess_text(self, text: str) -> str:
        """
        预处理文本
        
        Args:
            text: 原始文本
            
        Returns:
            预处理后的文本
        """
        # 去除标点符号和特殊字符
        text = re.sub(r'[^\w\s]', '', text)
        # 转换为小写
        text = text.lower()
        # 去除多余空格
        text = ' '.join(text.split())
        return text
    
    def calculate_similarity_simple(self, text1: str, text2: str) -> float:
        """
        使用简单方法计算文本相似度
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            相似度分数 (0-1)
        """
        # 预处理文本
        text1 = self.preprocess_text(text1)
        text2 = self.preprocess_text(text2)
        
        # 使用difflib计算相似度
        similarity = difflib.SequenceMatcher(None, text1, text2).ratio()
        return similarity
    
    def calculate_similarity_tfidf(self, text1: str, text2: str) -> float:
        """
        使用TF-IDF向量计算文本相似度
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            相似度分数 (0-1)
        """
        # 预处理文本
        text1 = self.preprocess_text(text1)
        text2 = self.preprocess_text(text2)
        
        # 中文分词
        words1 = ' '.join(jieba.cut(text1))
        words2 = ' '.join(jieba.cut(text2))
        
        # 计算TF-IDF向量
        try:
            tfidf_matrix = self.vectorizer.fit_transform([words1, words2])
            similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
            return float(similarity)
        except:
            # 如果TF-IDF计算失败，回退到简单方法
            return self.calculate_similarity_simple(text1, text2)
    
    def calculate_keyword_similarity(self, text1: str, text2: str, 
                                   keywords: List[str]) -> Dict[str, float]:
        """
        计算关键词相似度
        
        Args:
            text1: 文本1
            text2: 文本2
            keywords: 关键词列表
            
        Returns:
            关键词相似度字典
        """
        text1 = self.preprocess_text(text1).lower()
        text2 = self.preprocess_text(text2).lower()
        
        keyword_scores = {}
        
        for keyword in keywords:
            keyword = keyword.lower()
            # 检查关键词是否在两个文本中都出现
            in_text1 = keyword in text1
            in_text2 = keyword in text2
            
            if in_text1 and in_text2:
                keyword_scores[keyword] = 1.0
            elif in_text1 or in_text2:
                keyword_scores[keyword] = 0.5
            else:
                keyword_scores[keyword] = 0.0
        
        return keyword_scores
    
    def analyze_text_quality(self, standard_text: str, actual_text: str, 
                           keywords: Optional[List[str]] = None) -> Dict:
        """
        分析文本质量
        
        Args:
            standard_text: 标准文本
            actual_text: 实际文本
            keywords: 关键词列表
            
        Returns:
            分析结果字典
        """
        # 计算整体相似度
        simple_similarity = self.calculate_similarity_simple(standard_text, actual_text)
        tfidf_similarity = self.calculate_similarity_tfidf(standard_text, actual_text)
        
        # 综合相似度（加权平均）
        overall_similarity = 0.4 * simple_similarity + 0.6 * tfidf_similarity
        
        # 关键词分析
        keyword_analysis = {}
        if keywords:
            keyword_scores = self.calculate_keyword_similarity(
                standard_text, actual_text, keywords
            )
            keyword_analysis = {
                'keyword_scores': keyword_scores,
                'keyword_coverage': sum(keyword_scores.values()) / len(keywords) if keywords else 0,
                'missing_keywords': [k for k, v in keyword_scores.items() if v == 0]
            }
        
        # 长度分析
        len_ratio = min(len(actual_text), len(standard_text)) / max(len(actual_text), len(standard_text))
        
        # 生成评分等级
        score_grade = self._get_text_score_grade(overall_similarity)
        
        return {
            'overall_similarity': overall_similarity,
            'simple_similarity': simple_similarity,
            'tfidf_similarity': tfidf_similarity,
            'score_grade': score_grade,
            'length_ratio': len_ratio,
            'keyword_analysis': keyword_analysis,
            'recommendations': self._generate_text_recommendations(
                overall_similarity, keyword_analysis, len_ratio
            )
        }
    
    def _get_text_score_grade(self, score: float) -> str:
        """根据分数获取等级"""
        if score >= 0.9:
            return "优秀"
        elif score >= 0.8:
            return "良好"
        elif score >= 0.7:
            return "中等"
        elif score >= 0.6:
            return "及格"
        else:
            return "需要改进"
    
    def _generate_text_recommendations(self, similarity: float, 
                                     keyword_analysis: Dict, 
                                     length_ratio: float) -> List[str]:
        """生成文本改进建议"""
        recommendations = []
        
        if similarity < 0.7:
            recommendations.append("报告内容与标准差距较大，建议重新组织语言")
        
        if keyword_analysis and keyword_analysis.get('keyword_coverage', 1) < 0.8:
            missing = keyword_analysis.get('missing_keywords', [])
            if missing:
                recommendations.append(f"缺少关键词: {', '.join(missing[:3])}")
        
        if length_ratio < 0.7:
            recommendations.append("报告内容过于简短，建议补充更多细节")
        elif length_ratio > 1.3:
            recommendations.append("报告内容过于冗长，建议精简表达")
        
        if not recommendations:
            recommendations.append("报告质量良好，表达清晰！")
        
        return recommendations


class VoiceReportAnalyzer:
    """语音报告分析器"""
    
    def __init__(self):
        """初始化语音报告分析器"""
        self.speech_recognizer = SpeechRecognizer()
        self.text_calculator = TextSimilarityCalculator()
    
    def analyze_voice_report(self, standard_report: str, 
                           keywords: Optional[List[str]] = None,
                           recording_duration: Optional[float] = None) -> Dict:
        """
        分析语音报告
        
        Args:
            standard_report: 标准报告文本
            keywords: 关键词列表
            recording_duration: 录音时长
            
        Returns:
            分析结果
        """
        print("准备录制语音报告...")
        
        # 录制并识别语音
        recognized_text = self.speech_recognizer.record_and_recognize(recording_duration)
        
        if not recognized_text:
            return {
                'success': False,
                'error': '语音识别失败',
                'recognized_text': '',
                'analysis': {}
            }
        
        print(f"识别的文本: {recognized_text}")
        
        # 分析文本质量
        analysis = self.text_calculator.analyze_text_quality(
            standard_report, recognized_text, keywords
        )
        
        return {
            'success': True,
            'recognized_text': recognized_text,
            'analysis': analysis
        }


def test_speech_recognition():
    """测试语音识别功能"""
    analyzer = VoiceReportAnalyzer()
    
    standard_report = "设备运行正常，所有指标都在正常范围内，操作流程按照标准执行，没有发现异常情况。"
    keywords = ["设备", "正常", "指标", "操作", "流程", "标准"]
    
    print("语音报告分析测试")
    print(f"标准报告: {standard_report}")
    print("请开始录制您的报告（5秒）...")
    
    result = analyzer.analyze_voice_report(standard_report, keywords, 5.0)
    
    if result['success']:
        print(f"\n识别文本: {result['recognized_text']}")
        print(f"整体相似度: {result['analysis']['overall_similarity']:.3f}")
        print(f"评分等级: {result['analysis']['score_grade']}")
        print("改进建议:")
        for rec in result['analysis']['recommendations']:
            print(f"  - {rec}")
    else:
        print(f"分析失败: {result['error']}")


if __name__ == "__main__":
    test_speech_recognition()
