"""
工人操作视频打分工具主程序
整合姿态检测、动作相似度计算、语音识别等功能
"""

import cv2
import json
import os
import time
import argparse
from typing import Dict, List, Optional, Tuple
import numpy as np

from pose_detection import PoseDetector
from action_similarity import ActionSimilarityCalculator, load_standard_action
from speech_recognition import VoiceReportAnalyzer
from video_capture import VideoCapture, ActionRecorder


class WorkerEvaluationSystem:
    """工人操作评估系统"""
    
    def __init__(self):
        """初始化评估系统"""
        self.pose_detector = PoseDetector()
        self.similarity_calculator = ActionSimilarityCalculator()
        self.voice_analyzer = VoiceReportAnalyzer()
        self.video_capture = VideoCapture()
        self.action_recorder = ActionRecorder(self.video_capture)
        
        # 加载标准动作数据
        self.standard_actions = self._load_standard_actions()
        self.standard_reports = self._load_standard_reports()
        
        # 评估结果
        self.current_evaluation = {}
    
    def _load_standard_actions(self) -> Dict:
        """加载标准动作数据"""
        actions = {}
        actions_dir = os.path.join(os.path.dirname(__file__), "..", "data", "standard_actions")
        
        if not os.path.exists(actions_dir):
            print(f"标准动作目录不存在: {actions_dir}")
            return actions
        
        for action_name in os.listdir(actions_dir):
            action_path = os.path.join(actions_dir, action_name)
            if os.path.isdir(action_path):
                poses_file = os.path.join(action_path, "poses.json")
                if os.path.exists(poses_file):
                    action_data = load_standard_action(poses_file)
                    if action_data:
                        # 转换为关键点格式
                        pose_sequence = []
                        for pose_frame in action_data.get("poses", []):
                            keypoints = pose_frame.get("keypoints", {})
                            pose_sequence.append(keypoints)
                        actions[action_name] = pose_sequence
                        print(f"加载标准动作: {action_name} ({len(pose_sequence)} 帧)")
        
        return actions
    
    def _load_standard_reports(self) -> Dict:
        """加载标准报告文本"""
        reports = {}
        sample_dir = os.path.join(os.path.dirname(__file__), "..", "data", "training_data", "sample_data")
        
        if not os.path.exists(sample_dir):
            print(f"示例数据目录不存在: {sample_dir}")
            return reports
        
        for filename in os.listdir(sample_dir):
            if filename.endswith("_report.txt"):
                report_name = filename.replace("_report.txt", "")
                file_path = os.path.join(sample_dir, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # 提取关键词
                        lines = content.split('\n')
                        keywords = []
                        for line in lines:
                            if line.startswith("关键词："):
                                keywords = [kw.strip() for kw in line.replace("关键词：", "").split(',')]
                                break
                        
                        reports[report_name] = {
                            'text': content,
                            'keywords': keywords
                        }
                        print(f"加载标准报告: {report_name}")
                except Exception as e:
                    print(f"加载报告失败 {filename}: {e}")
        
        return reports
    
    def evaluate_action(self, action_name: str, duration: float = 5.0) -> Dict:
        """
        评估工人动作
        
        Args:
            action_name: 动作名称
            duration: 录制时长
            
        Returns:
            评估结果
        """
        if action_name not in self.standard_actions:
            return {
                'success': False,
                'error': f'未找到标准动作: {action_name}',
                'available_actions': list(self.standard_actions.keys())
            }
        
        print(f"开始评估动作: {action_name}")
        print(f"录制时长: {duration}秒")
        
        # 初始化摄像头
        if not self.video_capture.initialize_camera():
            return {'success': False, 'error': '摄像头初始化失败'}
        
        # 开始视频流
        self.video_capture.start_streaming()
        
        # 倒计时
        print("准备录制，3秒后开始...")
        for i in range(3, 0, -1):
            print(f"{i}...")
            time.sleep(1)
        
        print("开始录制动作！")
        
        # 录制动作
        recorded_frames = self.action_recorder.record_action_sequence(duration)
        
        if not recorded_frames:
            self.video_capture.release()
            return {'success': False, 'error': '录制失败'}
        
        # 提取姿态序列
        actual_poses = []
        for frame in recorded_frames:
            pose_data = self.pose_detector.detect_pose(frame)
            if pose_data:
                key_joints = self.pose_detector.extract_key_joints(pose_data[0])
                actual_poses.append(key_joints)
        
        self.video_capture.release()
        
        if not actual_poses:
            return {'success': False, 'error': '未检测到有效姿态'}
        
        # 计算相似度
        standard_sequence = self.standard_actions[action_name]
        analysis = self.similarity_calculator.analyze_action_quality(
            standard_sequence, actual_poses
        )
        
        result = {
            'success': True,
            'action_name': action_name,
            'recorded_frames': len(recorded_frames),
            'detected_poses': len(actual_poses),
            'analysis': analysis,
            'timestamp': time.time()
        }
        
        self.current_evaluation['action'] = result
        return result
    
    def evaluate_voice_report(self, report_name: str, duration: float = 10.0) -> Dict:
        """
        评估语音报告
        
        Args:
            report_name: 报告名称
            duration: 录音时长
            
        Returns:
            评估结果
        """
        if report_name not in self.standard_reports:
            return {
                'success': False,
                'error': f'未找到标准报告: {report_name}',
                'available_reports': list(self.standard_reports.keys())
            }
        
        print(f"开始评估语音报告: {report_name}")
        print(f"录音时长: {duration}秒")
        
        standard_report = self.standard_reports[report_name]
        
        # 分析语音报告
        result = self.voice_analyzer.analyze_voice_report(
            standard_report['text'],
            standard_report['keywords'],
            duration
        )
        
        if result['success']:
            result['report_name'] = report_name
            result['timestamp'] = time.time()
            self.current_evaluation['voice'] = result
        
        return result
    
    def get_overall_score(self) -> Dict:
        """
        计算综合评分
        
        Returns:
            综合评分结果
        """
        if 'action' not in self.current_evaluation or 'voice' not in self.current_evaluation:
            return {
                'success': False,
                'error': '需要完成动作和语音评估',
                'completed': {
                    'action': 'action' in self.current_evaluation,
                    'voice': 'voice' in self.current_evaluation
                }
            }
        
        action_result = self.current_evaluation['action']
        voice_result = self.current_evaluation['voice']
        
        # 计算权重分数
        action_score = action_result['analysis']['overall_score']
        voice_score = voice_result['analysis']['overall_similarity']
        
        # 综合评分 (动作70%, 语音30%)
        overall_score = 0.7 * action_score + 0.3 * voice_score
        
        # 确定等级
        if overall_score >= 0.9:
            grade = "优秀"
        elif overall_score >= 0.8:
            grade = "良好"
        elif overall_score >= 0.7:
            grade = "中等"
        elif overall_score >= 0.6:
            grade = "及格"
        else:
            grade = "需要改进"
        
        # 生成综合建议
        recommendations = []
        recommendations.extend(action_result['analysis']['recommendations'])
        recommendations.extend(voice_result['analysis']['recommendations'])
        
        return {
            'success': True,
            'overall_score': overall_score,
            'grade': grade,
            'action_score': action_score,
            'voice_score': voice_score,
            'recommendations': recommendations,
            'detailed_results': {
                'action': action_result,
                'voice': voice_result
            }
        }
    
    def save_evaluation_report(self, filename: Optional[str] = None) -> str:
        """
        保存评估报告
        
        Args:
            filename: 报告文件名
            
        Returns:
            保存的文件路径
        """
        if filename is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"evaluation_report_{timestamp}.json"
        
        overall_result = self.get_overall_score()
        
        report_data = {
            'timestamp': time.time(),
            'overall_evaluation': overall_result,
            'individual_evaluations': self.current_evaluation
        }
        
        reports_dir = os.path.join(os.path.dirname(__file__), "..", "reports")
        os.makedirs(reports_dir, exist_ok=True)
        
        filepath = os.path.join(reports_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        return filepath
    
    def interactive_evaluation(self):
        """交互式评估界面"""
        print("=" * 50)
        print("工人操作视频打分工具")
        print("=" * 50)
        
        while True:
            print("\n可用功能:")
            print("1. 动作评估")
            print("2. 语音报告评估")
            print("3. 查看综合评分")
            print("4. 保存评估报告")
            print("5. 退出")
            
            choice = input("\n请选择功能 (1-5): ").strip()
            
            if choice == '1':
                self._interactive_action_evaluation()
            elif choice == '2':
                self._interactive_voice_evaluation()
            elif choice == '3':
                self._show_overall_score()
            elif choice == '4':
                self._save_report()
            elif choice == '5':
                print("退出程序")
                break
            else:
                print("无效选择，请重新输入")
    
    def _interactive_action_evaluation(self):
        """交互式动作评估"""
        print("\n可用的标准动作:")
        for i, action_name in enumerate(self.standard_actions.keys(), 1):
            print(f"{i}. {action_name}")
        
        if not self.standard_actions:
            print("没有可用的标准动作数据")
            return
        
        try:
            choice = int(input("选择动作编号: ")) - 1
            action_names = list(self.standard_actions.keys())
            
            if 0 <= choice < len(action_names):
                action_name = action_names[choice]
                duration = float(input("录制时长(秒，默认5): ") or "5")
                
                result = self.evaluate_action(action_name, duration)
                
                if result['success']:
                    print(f"\n动作评估完成!")
                    print(f"动作名称: {result['action_name']}")
                    print(f"录制帧数: {result['recorded_frames']}")
                    print(f"检测姿态: {result['detected_poses']}")
                    print(f"相似度分数: {result['analysis']['overall_score']:.3f}")
                    print(f"评分等级: {result['analysis']['score_grade']}")
                    print("改进建议:")
                    for rec in result['analysis']['recommendations']:
                        print(f"  - {rec}")
                else:
                    print(f"评估失败: {result['error']}")
            else:
                print("无效的动作编号")
        except ValueError:
            print("请输入有效的数字")
    
    def _interactive_voice_evaluation(self):
        """交互式语音评估"""
        print("\n可用的标准报告:")
        for i, report_name in enumerate(self.standard_reports.keys(), 1):
            print(f"{i}. {report_name}")
        
        if not self.standard_reports:
            print("没有可用的标准报告数据")
            return
        
        try:
            choice = int(input("选择报告编号: ")) - 1
            report_names = list(self.standard_reports.keys())
            
            if 0 <= choice < len(report_names):
                report_name = report_names[choice]
                duration = float(input("录音时长(秒，默认10): ") or "10")
                
                result = self.evaluate_voice_report(report_name, duration)
                
                if result['success']:
                    print(f"\n语音评估完成!")
                    print(f"报告名称: {result['report_name']}")
                    print(f"识别文本: {result['recognized_text']}")
                    print(f"相似度分数: {result['analysis']['overall_similarity']:.3f}")
                    print(f"评分等级: {result['analysis']['score_grade']}")
                    print("改进建议:")
                    for rec in result['analysis']['recommendations']:
                        print(f"  - {rec}")
                else:
                    print(f"评估失败: {result['error']}")
            else:
                print("无效的报告编号")
        except ValueError:
            print("请输入有效的数字")
    
    def _show_overall_score(self):
        """显示综合评分"""
        result = self.get_overall_score()
        
        if result['success']:
            print(f"\n综合评估结果:")
            print(f"综合评分: {result['overall_score']:.3f}")
            print(f"评分等级: {result['grade']}")
            print(f"动作分数: {result['action_score']:.3f}")
            print(f"语音分数: {result['voice_score']:.3f}")
            print("综合建议:")
            for rec in result['recommendations']:
                print(f"  - {rec}")
        else:
            print(f"无法计算综合评分: {result['error']}")
            print("完成情况:")
            for task, completed in result['completed'].items():
                status = "已完成" if completed else "未完成"
                print(f"  {task}: {status}")
    
    def _save_report(self):
        """保存报告"""
        try:
            filepath = self.save_evaluation_report()
            print(f"评估报告已保存到: {filepath}")
        except Exception as e:
            print(f"保存报告失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="工人操作视频打分工具")
    parser.add_argument("--mode", choices=["interactive", "action", "voice", "demo"], 
                       default="interactive", help="运行模式")
    parser.add_argument("--action", help="动作名称")
    parser.add_argument("--report", help="报告名称")
    parser.add_argument("--duration", type=float, default=5.0, help="录制时长")
    
    args = parser.parse_args()
    
    # 创建评估系统
    system = WorkerEvaluationSystem()
    
    if args.mode == "interactive":
        system.interactive_evaluation()
    elif args.mode == "action" and args.action:
        result = system.evaluate_action(args.action, args.duration)
        print(json.dumps(result, ensure_ascii=False, indent=2))
    elif args.mode == "voice" and args.report:
        result = system.evaluate_voice_report(args.report, args.duration)
        print(json.dumps(result, ensure_ascii=False, indent=2))
    elif args.mode == "demo":
        print("演示模式 - 创建示例数据")
        from data.training_data.create_training_data import save_training_data
        save_training_data()
        print("示例数据创建完成，请重新运行程序")
    else:
        print("参数错误，使用 --help 查看帮助")


if __name__ == "__main__":
    main()
