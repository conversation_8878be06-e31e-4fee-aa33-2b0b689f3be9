"""
创建训练数据脚本
生成示例标准动作数据和报告文本
"""

import json
import os
import numpy as np
from typing import List, Dict
import cv2
import sys

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from pose_detection import PoseDetector
from video_capture import VideoCapture, ActionRecorder


def create_sample_pose_data():
    """创建示例姿态数据"""
    
    # 定义几个标准动作的关键帧
    actions = {
        "equipment_check": {
            "name": "设备检查动作",
            "description": "工人检查设备状态的标准动作序列",
            "duration": 5.0,
            "keyframes": [
                # 初始站立姿态
                {
                    "left_shoulder": [320, 200], "right_shoulder": [380, 200],
                    "left_elbow": [300, 250], "right_elbow": [400, 250],
                    "left_wrist": [280, 300], "right_wrist": [420, 300],
                    "left_hip": [330, 350], "right_hip": [370, 350],
                    "left_knee": [325, 450], "right_knee": [375, 450],
                    "left_ankle": [320, 550], "right_ankle": [380, 550]
                },
                # 伸手检查动作
                {
                    "left_shoulder": [320, 200], "right_shoulder": [380, 200],
                    "left_elbow": [280, 220], "right_elbow": [420, 220],
                    "left_wrist": [240, 200], "right_wrist": [460, 200],
                    "left_hip": [330, 350], "right_hip": [370, 350],
                    "left_knee": [325, 450], "right_knee": [375, 450],
                    "left_ankle": [320, 550], "right_ankle": [380, 550]
                },
                # 低头查看动作
                {
                    "left_shoulder": [320, 220], "right_shoulder": [380, 220],
                    "left_elbow": [300, 270], "right_elbow": [400, 270],
                    "left_wrist": [280, 320], "right_wrist": [420, 320],
                    "left_hip": [330, 350], "right_hip": [370, 350],
                    "left_knee": [325, 450], "right_knee": [375, 450],
                    "left_ankle": [320, 550], "right_ankle": [380, 550]
                },
                # 记录动作
                {
                    "left_shoulder": [320, 200], "right_shoulder": [380, 200],
                    "left_elbow": [340, 250], "right_elbow": [420, 220],
                    "left_wrist": [360, 300], "right_wrist": [460, 200],
                    "left_hip": [330, 350], "right_hip": [370, 350],
                    "left_knee": [325, 450], "right_knee": [375, 450],
                    "left_ankle": [320, 550], "right_ankle": [380, 550]
                },
                # 回到初始姿态
                {
                    "left_shoulder": [320, 200], "right_shoulder": [380, 200],
                    "left_elbow": [300, 250], "right_elbow": [400, 250],
                    "left_wrist": [280, 300], "right_wrist": [420, 300],
                    "left_hip": [330, 350], "right_hip": [370, 350],
                    "left_knee": [325, 450], "right_knee": [375, 450],
                    "left_ankle": [320, 550], "right_ankle": [380, 550]
                }
            ]
        },
        
        "button_operation": {
            "name": "按钮操作动作",
            "description": "操作设备按钮的标准动作序列",
            "duration": 3.0,
            "keyframes": [
                # 准备姿态
                {
                    "left_shoulder": [320, 200], "right_shoulder": [380, 200],
                    "left_elbow": [300, 250], "right_elbow": [400, 250],
                    "left_wrist": [280, 300], "right_wrist": [420, 300],
                    "left_hip": [330, 350], "right_hip": [370, 350],
                    "left_knee": [325, 450], "right_knee": [375, 450],
                    "left_ankle": [320, 550], "right_ankle": [380, 550]
                },
                # 伸手按按钮
                {
                    "left_shoulder": [320, 200], "right_shoulder": [380, 200],
                    "left_elbow": [300, 250], "right_elbow": [420, 180],
                    "left_wrist": [280, 300], "right_wrist": [450, 150],
                    "left_hip": [330, 350], "right_hip": [370, 350],
                    "left_knee": [325, 450], "right_knee": [375, 450],
                    "left_ankle": [320, 550], "right_ankle": [380, 550]
                },
                # 按下按钮
                {
                    "left_shoulder": [320, 200], "right_shoulder": [380, 200],
                    "left_elbow": [300, 250], "right_elbow": [425, 185],
                    "left_wrist": [280, 300], "right_wrist": [455, 160],
                    "left_hip": [330, 350], "right_hip": [370, 350],
                    "left_knee": [325, 450], "right_knee": [375, 450],
                    "left_ankle": [320, 550], "right_ankle": [380, 550]
                },
                # 收回手
                {
                    "left_shoulder": [320, 200], "right_shoulder": [380, 200],
                    "left_elbow": [300, 250], "right_elbow": [400, 250],
                    "left_wrist": [280, 300], "right_wrist": [420, 300],
                    "left_hip": [330, 350], "right_hip": [370, 350],
                    "left_knee": [325, 450], "right_knee": [375, 450],
                    "left_ankle": [320, 550], "right_ankle": [380, 550]
                }
            ]
        }
    }
    
    return actions


def interpolate_poses(keyframes: List[Dict], total_frames: int) -> List[Dict]:
    """
    在关键帧之间插值生成完整的姿态序列
    
    Args:
        keyframes: 关键帧列表
        total_frames: 总帧数
        
    Returns:
        插值后的姿态序列
    """
    if len(keyframes) < 2:
        return keyframes * total_frames
    
    poses = []
    keyframe_indices = np.linspace(0, total_frames - 1, len(keyframes))
    
    for frame_idx in range(total_frames):
        # 找到当前帧对应的关键帧区间
        for i in range(len(keyframe_indices) - 1):
            if keyframe_indices[i] <= frame_idx <= keyframe_indices[i + 1]:
                # 计算插值权重
                t = (frame_idx - keyframe_indices[i]) / (keyframe_indices[i + 1] - keyframe_indices[i])
                
                # 插值计算
                interpolated_pose = {}
                for joint in keyframes[i].keys():
                    if joint in keyframes[i + 1]:
                        x1, y1 = keyframes[i][joint]
                        x2, y2 = keyframes[i + 1][joint]
                        
                        x = x1 + t * (x2 - x1)
                        y = y1 + t * (y2 - y1)
                        
                        interpolated_pose[joint] = [float(x), float(y)]
                
                poses.append(interpolated_pose)
                break
        else:
            # 如果超出范围，使用最后一个关键帧
            poses.append(keyframes[-1].copy())
    
    return poses


def create_action_data(action_info: Dict, fps: int = 30) -> Dict:
    """
    创建完整的动作数据
    
    Args:
        action_info: 动作信息
        fps: 帧率
        
    Returns:
        完整的动作数据
    """
    total_frames = int(action_info["duration"] * fps)
    poses = interpolate_poses(action_info["keyframes"], total_frames)
    
    # 构建完整的动作数据
    action_data = {
        "action_name": action_info["name"],
        "description": action_info["description"],
        "duration": action_info["duration"],
        "fps": fps,
        "total_frames": total_frames,
        "poses": []
    }
    
    for i, pose in enumerate(poses):
        frame_data = {
            "frame_id": i,
            "timestamp": i / fps,
            "keypoints": pose
        }
        action_data["poses"].append(frame_data)
    
    return action_data


def create_sample_reports():
    """创建示例报告文本"""
    reports = {
        "equipment_check_report": {
            "standard": """设备检查报告：
设备外观完好，无明显损坏或异常。
所有指示灯显示正常状态，绿灯亮起。
操作按钮响应正常，无卡顿现象。
设备运行参数均在标准范围内。
温度指标正常，压力读数稳定。
未发现任何安全隐患或故障征象。
设备可以正常投入使用。

关键词：设备,检查,正常,指示灯,按钮,参数,温度,压力,安全,故障""",
            
            "variations": [
                """设备状态良好，各项指标正常。检查了外观、指示灯和操作按钮，都没有问题。运行参数稳定，可以继续使用。

关键词：设备,状态,指标,正常,检查,外观,指示灯,按钮,参数,稳定""",
                
                """完成设备检查，发现一切正常。外观无损坏，指示灯绿色，按钮灵敏。温度和压力都在正常范围。设备运行良好。

关键词：设备,检查,正常,外观,指示灯,按钮,温度,压力,运行"""
            ]
        },
        
        "button_operation_report": {
            "standard": """按钮操作报告：
按照标准操作流程执行按钮操作。
首先确认设备处于安全状态。
定位目标操作按钮位置。
使用正确的手势按下按钮。
确认按钮响应和状态变化。
操作完成后检查设备反应。
整个操作过程顺利完成。

关键词：按钮,操作,流程,安全,定位,手势,响应,状态,检查,完成""",
            
            "variations": [
                """按钮操作顺利完成。先确认安全，然后准确按下按钮，设备响应正常。操作符合标准流程。

关键词：按钮,操作,完成,安全,按下,响应,正常,流程""",
                
                """执行了按钮操作任务。确保安全后，精确操作按钮，设备状态变化正常。操作成功。

关键词：按钮,操作,任务,安全,精确,状态,变化,成功"""
            ]
        }
    }
    
    return reports


def save_training_data():
    """保存训练数据到文件"""
    
    # 创建目录
    base_dir = os.path.dirname(__file__)
    standard_actions_dir = os.path.join(base_dir, "..", "standard_actions")
    sample_data_dir = os.path.join(base_dir, "sample_data")
    
    os.makedirs(standard_actions_dir, exist_ok=True)
    os.makedirs(sample_data_dir, exist_ok=True)
    
    # 创建动作数据
    actions = create_sample_pose_data()
    
    for action_key, action_info in actions.items():
        action_dir = os.path.join(standard_actions_dir, action_key)
        os.makedirs(action_dir, exist_ok=True)
        
        # 保存姿态数据
        action_data = create_action_data(action_info)
        poses_file = os.path.join(action_dir, "poses.json")
        with open(poses_file, 'w', encoding='utf-8') as f:
            json.dump(action_data, f, ensure_ascii=False, indent=2)
        
        # 保存动作描述
        desc_file = os.path.join(action_dir, "description.txt")
        with open(desc_file, 'w', encoding='utf-8') as f:
            f.write(action_info["description"])
        
        print(f"创建动作数据: {action_key}")
    
    # 创建报告数据
    reports = create_sample_reports()
    
    for report_key, report_data in reports.items():
        report_file = os.path.join(sample_data_dir, f"{report_key}.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_data["standard"])
        
        # 保存变体
        for i, variation in enumerate(report_data["variations"]):
            var_file = os.path.join(sample_data_dir, f"{report_key}_var_{i+1}.txt")
            with open(var_file, 'w', encoding='utf-8') as f:
                f.write(variation)
        
        print(f"创建报告数据: {report_key}")
    
    # 创建示例配置文件
    config = {
        "actions": list(actions.keys()),
        "reports": list(reports.keys()),
        "fps": 30,
        "similarity_threshold": 0.7,
        "keywords_weight": 0.3
    }
    
    config_file = os.path.join(sample_data_dir, "config.json")
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print("训练数据创建完成！")
    print(f"标准动作数据保存在: {standard_actions_dir}")
    print(f"示例数据保存在: {sample_data_dir}")


def record_real_action_data():
    """录制真实的动作数据"""
    print("开始录制真实动作数据...")
    
    try:
        # 初始化设备
        detector = PoseDetector()
        capture = VideoCapture()
        recorder = ActionRecorder(capture)
        
        if not capture.initialize_camera():
            print("摄像头初始化失败")
            return
        
        # 开始视频流
        capture.start_streaming()
        
        actions_to_record = ["equipment_check", "button_operation"]
        
        for action_name in actions_to_record:
            input(f"准备录制 '{action_name}' 动作，按回车开始...")
            
            print("3秒后开始录制...")
            for i in range(3, 0, -1):
                print(f"{i}...")
                time.sleep(1)
            
            print("开始录制！(5秒)")
            frames = recorder.record_action_sequence(5.0)
            
            if frames:
                # 提取姿态数据
                poses = []
                for i, frame in enumerate(frames):
                    pose_data = detector.detect_pose(frame)
                    if pose_data:
                        key_joints = detector.extract_key_joints(pose_data[0])
                        frame_data = {
                            "frame_id": i,
                            "timestamp": i / 30.0,
                            "keypoints": key_joints
                        }
                        poses.append(frame_data)
                
                # 保存数据
                action_data = {
                    "action_name": action_name,
                    "description": f"录制的{action_name}动作",
                    "duration": 5.0,
                    "fps": 30,
                    "total_frames": len(poses),
                    "poses": poses
                }
                
                # 保存到文件
                base_dir = os.path.dirname(__file__)
                action_dir = os.path.join(base_dir, "..", "standard_actions", f"recorded_{action_name}")
                os.makedirs(action_dir, exist_ok=True)
                
                poses_file = os.path.join(action_dir, "poses.json")
                with open(poses_file, 'w', encoding='utf-8') as f:
                    json.dump(action_data, f, ensure_ascii=False, indent=2)
                
                print(f"动作 '{action_name}' 录制完成，保存了 {len(poses)} 帧数据")
            else:
                print(f"动作 '{action_name}' 录制失败")
        
        capture.release()
        print("真实动作数据录制完成！")
        
    except Exception as e:
        print(f"录制过程中出现错误: {e}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="创建训练数据")
    parser.add_argument("--mode", choices=["sample", "record"], default="sample",
                       help="数据创建模式：sample=创建示例数据，record=录制真实数据")
    
    args = parser.parse_args()
    
    if args.mode == "sample":
        save_training_data()
    elif args.mode == "record":
        import time
        record_real_action_data()
    else:
        print("未知模式，使用 --help 查看帮助")
