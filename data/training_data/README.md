# 训练数据说明

## 数据集结构

```
training_data/
├── README.md                    # 本文件
├── standard_actions/           # 标准动作数据
│   ├── action_1/              # 动作1
│   │   ├── poses.json         # 姿态关键点序列
│   │   ├── description.txt    # 动作描述
│   │   └── frames/           # 视频帧（可选）
│   ├── action_2/              # 动作2
│   └── ...
├── sample_data/               # 示例数据
│   ├── sample_poses.json     # 示例姿态数据
│   └── sample_report.txt     # 示例报告文本
└── create_training_data.py   # 数据创建脚本
```

## 数据格式说明

### 姿态数据格式 (poses.json)

```json
{
  "action_name": "设备检查动作",
  "description": "工人检查设备状态的标准动作",
  "duration": 5.0,
  "fps": 30,
  "poses": [
    {
      "frame_id": 0,
      "timestamp": 0.0,
      "keypoints": {
        "left_shoulder": [100.5, 200.3],
        "right_shoulder": [200.1, 200.8],
        "left_elbow": [80.2, 250.5],
        "right_elbow": [220.3, 250.1],
        "left_wrist": [60.1, 300.2],
        "right_wrist": [240.5, 300.8],
        "left_hip": [120.3, 350.1],
        "right_hip": [180.2, 350.5],
        "left_knee": [115.1, 450.3],
        "right_knee": [185.5, 450.2],
        "left_ankle": [110.2, 550.1],
        "right_ankle": [190.3, 550.8]
      }
    }
  ]
}
```

### 报告文本格式 (description.txt)

```
标准操作报告：
设备运行状态正常，所有指标均在标准范围内。
按照操作流程完成了以下步骤：
1. 检查设备外观
2. 确认指示灯状态
3. 测试操作按钮
4. 记录运行参数
未发现异常情况，设备可正常使用。

关键词：设备,正常,指标,操作,流程,检查,状态,参数
```

## 模型训练方法

### 1. 数据准备

```bash
# 创建示例数据
python data/training_data/create_training_data.py

# 录制标准动作
python src/main.py --mode record_standard
```

### 2. 模型训练

YOLOv8 pose模型已经预训练，可以直接使用。如需自定义训练：

```bash
# 安装ultralytics
pip install ultralytics

# 使用预训练模型
from ultralytics import YOLO
model = YOLO('yolov8n-pose.pt')

# 如需自定义训练（需要标注数据）
# model.train(data='path/to/dataset.yaml', epochs=100)
```

### 3. 标准动作数据收集

1. **录制标准动作视频**
   - 使用高质量摄像头
   - 确保光线充足
   - 动作清晰、标准
   - 录制多个角度

2. **提取姿态关键点**
   ```python
   from src.pose_detection import PoseDetector
   from src.video_capture import VideoCapture
   
   detector = PoseDetector()
   capture = VideoCapture()
   
   # 录制并提取关键点
   frames = capture.capture_frames(duration=5.0)
   poses = []
   for frame in frames:
       pose_data = detector.detect_pose(frame)
       if pose_data:
           poses.append(pose_data[0])
   ```

3. **保存标准动作数据**
   ```python
   import json
   
   action_data = {
       "action_name": "设备检查",
       "description": "标准设备检查动作",
       "poses": poses
   }
   
   with open('data/standard_actions/check_equipment/poses.json', 'w') as f:
       json.dump(action_data, f, indent=2)
   ```

## 数据质量要求

### 视频数据
- 分辨率：至少640x480
- 帧率：30fps
- 时长：3-10秒
- 格式：MP4, AVI

### 姿态数据
- 关键点检测置信度 > 0.5
- 至少包含12个主要关节点
- 动作连贯，无遮挡

### 文本数据
- 中文字符编码：UTF-8
- 长度：50-500字
- 包含关键操作词汇
- 语法正确，表达清晰

## 扩展数据集

### 添加新动作类型
1. 在 `standard_actions/` 下创建新目录
2. 录制标准动作视频
3. 提取姿态关键点
4. 编写动作描述
5. 更新配置文件

### 数据增强
- 镜像翻转
- 轻微旋转
- 缩放变换
- 噪声添加

## 评估指标

### 动作相似度
- 关键点距离误差
- 时序对齐质量
- 动作完整性

### 文本相似度
- 词汇覆盖率
- 语义相似度
- 关键词匹配率

## 注意事项

1. **隐私保护**：确保录制数据已获得授权
2. **数据安全**：敏感数据需要加密存储
3. **版本控制**：记录数据集版本和更新历史
4. **质量检查**：定期验证数据质量和标注准确性
