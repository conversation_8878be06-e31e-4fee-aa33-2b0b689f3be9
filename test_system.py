"""
系统功能测试脚本
验证各个模块的基本功能
"""

import os
import sys
import traceback

# 添加src目录到路径
sys.path.append('src')

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        import cv2
        print("✓ OpenCV导入成功")
    except ImportError as e:
        print(f"✗ OpenCV导入失败: {e}")
        return False
    
    try:
        from ultralytics import YOLO
        print("✓ Ultralytics导入成功")
    except ImportError as e:
        print(f"✗ Ultralytics导入失败: {e}")
        return False
    
    try:
        import speech_recognition as sr
        print("✓ SpeechRecognition导入成功")
    except ImportError as e:
        print(f"✗ SpeechRecognition导入失败: {e}")
        return False
    
    try:
        from pose_detection import PoseDetector
        from action_similarity import ActionSimilarityCalculator
        from speech_recognition import VoiceReportAnalyzer
        from video_capture import VideoCapture
        from main import WorkerEvaluationSystem
        print("✓ 自定义模块导入成功")
    except ImportError as e:
        print(f"✗ 自定义模块导入失败: {e}")
        return False
    
    return True


def test_pose_detection():
    """测试姿态检测模块"""
    print("\n测试姿态检测模块...")
    
    try:
        from pose_detection import PoseDetector
        
        detector = PoseDetector()
        print("✓ PoseDetector初始化成功")
        
        # 测试关键点字典转换
        import numpy as np
        test_keypoints = np.array([[100, 200, 0.9], [150, 250, 0.8]])
        keypoint_dict = detector._keypoints_to_dict(test_keypoints)
        
        if len(keypoint_dict) > 0:
            print("✓ 关键点转换功能正常")
        else:
            print("✗ 关键点转换功能异常")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 姿态检测模块测试失败: {e}")
        traceback.print_exc()
        return False


def test_action_similarity():
    """测试动作相似度计算模块"""
    print("\n测试动作相似度计算模块...")
    
    try:
        from action_similarity import ActionSimilarityCalculator
        
        calculator = ActionSimilarityCalculator()
        print("✓ ActionSimilarityCalculator初始化成功")
        
        # 测试姿态相似度计算
        pose1 = {
            'left_shoulder': (100, 200),
            'right_shoulder': (200, 200),
            'left_wrist': (80, 300),
            'right_wrist': (220, 300)
        }
        
        pose2 = {
            'left_shoulder': (105, 205),
            'right_shoulder': (195, 205),
            'left_wrist': (85, 305),
            'right_wrist': (215, 305)
        }
        
        similarity = calculator.calculate_pose_similarity(pose1, pose2)
        
        if 0 <= similarity <= 1:
            print(f"✓ 姿态相似度计算正常: {similarity:.3f}")
        else:
            print(f"✗ 姿态相似度计算异常: {similarity}")
            return False
        
        # 测试序列相似度
        seq1 = [pose1] * 5
        seq2 = [pose2] * 5
        
        seq_similarity = calculator.calculate_sequence_similarity(seq1, seq2)
        
        if 0 <= seq_similarity <= 1:
            print(f"✓ 序列相似度计算正常: {seq_similarity:.3f}")
        else:
            print(f"✗ 序列相似度计算异常: {seq_similarity}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 动作相似度计算模块测试失败: {e}")
        traceback.print_exc()
        return False


def test_text_similarity():
    """测试文本相似度计算"""
    print("\n测试文本相似度计算...")
    
    try:
        from speech_recognition import TextSimilarityCalculator
        
        calculator = TextSimilarityCalculator()
        print("✓ TextSimilarityCalculator初始化成功")
        
        # 测试文本相似度
        text1 = "设备运行正常，所有指标都在标准范围内。"
        text2 = "设备状态良好，各项指标正常。"
        
        similarity = calculator.calculate_similarity_simple(text1, text2)
        
        if 0 <= similarity <= 1:
            print(f"✓ 简单文本相似度计算正常: {similarity:.3f}")
        else:
            print(f"✗ 简单文本相似度计算异常: {similarity}")
            return False
        
        # 测试TF-IDF相似度
        tfidf_similarity = calculator.calculate_similarity_tfidf(text1, text2)
        
        if 0 <= tfidf_similarity <= 1:
            print(f"✓ TF-IDF相似度计算正常: {tfidf_similarity:.3f}")
        else:
            print(f"✗ TF-IDF相似度计算异常: {tfidf_similarity}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 文本相似度计算测试失败: {e}")
        traceback.print_exc()
        return False


def test_video_capture():
    """测试视频采集模块"""
    print("\n测试视频采集模块...")
    
    try:
        from video_capture import VideoCapture
        
        capture = VideoCapture()
        print("✓ VideoCapture初始化成功")
        
        # 测试摄像头初始化（不实际打开）
        print("✓ 视频采集模块基本功能正常")
        
        return True
    except Exception as e:
        print(f"✗ 视频采集模块测试失败: {e}")
        traceback.print_exc()
        return False


def test_training_data():
    """测试训练数据创建"""
    print("\n测试训练数据创建...")
    
    try:
        # 检查是否已有训练数据
        data_dir = "data/standard_actions"
        sample_dir = "data/training_data/sample_data"
        
        if os.path.exists(data_dir) and os.listdir(data_dir):
            print(f"✓ 标准动作数据存在: {len(os.listdir(data_dir))} 个动作")
        else:
            print("! 标准动作数据不存在，尝试创建...")
            from data.training_data.create_training_data import save_training_data
            save_training_data()
            print("✓ 标准动作数据创建成功")
        
        if os.path.exists(sample_dir) and os.listdir(sample_dir):
            sample_files = [f for f in os.listdir(sample_dir) if f.endswith('.txt')]
            print(f"✓ 示例报告数据存在: {len(sample_files)} 个报告")
        else:
            print("! 示例报告数据可能不完整")
        
        return True
    except Exception as e:
        print(f"✗ 训练数据测试失败: {e}")
        traceback.print_exc()
        return False


def test_main_system():
    """测试主系统"""
    print("\n测试主系统...")
    
    try:
        from main import WorkerEvaluationSystem
        
        system = WorkerEvaluationSystem()
        print("✓ WorkerEvaluationSystem初始化成功")
        
        print(f"✓ 加载标准动作: {len(system.standard_actions)} 个")
        print(f"✓ 加载标准报告: {len(system.standard_reports)} 个")
        
        if len(system.standard_actions) == 0:
            print("! 警告: 没有加载到标准动作数据")
        
        if len(system.standard_reports) == 0:
            print("! 警告: 没有加载到标准报告数据")
        
        return True
    except Exception as e:
        print(f"✗ 主系统测试失败: {e}")
        traceback.print_exc()
        return False


def test_helpers():
    """测试辅助函数"""
    print("\n测试辅助函数...")
    
    try:
        from utils.helpers import calculate_distance, calculate_angle, normalize_keypoints
        
        # 测试距离计算
        distance = calculate_distance((0, 0), (3, 4))
        if abs(distance - 5.0) < 0.001:
            print("✓ 距离计算功能正常")
        else:
            print(f"✗ 距离计算功能异常: {distance}")
            return False
        
        # 测试角度计算
        angle = calculate_angle((0, 0), (1, 0), (1, 1))
        if 89 <= angle <= 91:  # 应该是90度
            print("✓ 角度计算功能正常")
        else:
            print(f"✗ 角度计算功能异常: {angle}")
            return False
        
        # 测试关键点标准化
        keypoints = {
            'left_shoulder': (100, 200),
            'right_shoulder': (200, 200),
            'left_wrist': (80, 300)
        }
        
        normalized = normalize_keypoints(keypoints)
        if len(normalized) == len(keypoints):
            print("✓ 关键点标准化功能正常")
        else:
            print("✗ 关键点标准化功能异常")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 辅助函数测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("工人操作视频打分工具 - 系统功能测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("姿态检测", test_pose_detection),
        ("动作相似度计算", test_action_similarity),
        ("文本相似度计算", test_text_similarity),
        ("视频采集", test_video_capture),
        ("训练数据", test_training_data),
        ("主系统", test_main_system),
        ("辅助函数", test_helpers)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有测试通过！系统功能正常。")
        print("\n可以开始使用系统:")
        print("  python src/main.py")
        print("  python demo.py")
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
        print("\n建议:")
        print("  1. 检查依赖包是否正确安装")
        print("  2. 运行 python setup.py 重新安装")
        print("  3. 查看具体错误信息并修复")
    
    return passed == total


if __name__ == "__main__":
    main()
