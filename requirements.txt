# 核心依赖包
ultralytics>=8.0.0,<=8.0.34
opencv-python>=4.5.0,<5.0.0
numpy>=1.21.0,<1.25.0
torch>=1.13.0,<2.1.0
torchvision>=0.14.0,<0.16.0

# 语音识别相关
speechrecognition>=3.8.0
pyaudio>=0.2.11

# 机器学习和数据处理
scikit-learn>=1.0.0,<1.4.0
scipy>=1.7.0,<1.12.0

# 可视化和数据分析
matplotlib>=3.5.0,<3.8.0
seaborn>=0.11.0,<0.13.0
pandas>=1.3.0,<2.1.0

# 图像处理
Pillow>=8.0.0,<10.1.0

# 工具包
tqdm>=4.60.0
pyyaml>=5.4.0,<7.0.0

# 中文分词（用于文本相似度计算）
jieba>=0.42.0
