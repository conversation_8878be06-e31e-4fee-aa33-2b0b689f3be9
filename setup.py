"""
工人操作视频打分工具安装脚本
"""

import os
import sys
import subprocess
import platform


def check_python_version():
    """检查Python版本"""
    version = sys.version_info

    if version < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False

    if version >= (3, 12):
        print("警告: Python 3.12+可能存在兼容性问题")
        print("建议使用Python 3.8-3.11版本")
        print(f"当前版本: {sys.version}")

        response = input("是否继续安装? (y/N): ").lower().strip()
        if response != 'y':
            return False

    print(f"Python版本检查通过: {sys.version}")
    return True


def install_requirements():
    """安装依赖包"""
    print("正在安装依赖包...")

    try:
        # 升级pip
        print("升级pip...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])

        # 检查是否有requirements.txt文件
        if not os.path.exists("requirements.txt"):
            print("错误: requirements.txt文件不存在")
            return False

        # 安装requirements.txt中的包
        print("安装依赖包...")
        cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"]

        # 如果是某些特殊环境，添加额外参数
        if platform.system() == "Darwin" and platform.machine() == "arm64":
            # M1 Mac特殊处理
            print("检测到Apple Silicon Mac，使用特殊安装参数...")
            cmd.extend(["--no-cache-dir"])

        subprocess.check_call(cmd)

        print("依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"依赖包安装失败: {e}")
        print("\n可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 使用国内镜像源: pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/")
        print("3. 手动安装核心包: pip install ultralytics opencv-python")
        return False


def setup_directories():
    """创建必要的目录"""
    print("创建项目目录...")
    
    directories = [
        "data/standard_actions",
        "data/training_data/sample_data",
        "data/models",
        "reports",
        "logs"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"创建目录: {directory}")
    
    print("目录创建完成")


def download_yolo_model():
    """下载YOLOv8模型"""
    print("检查YOLOv8模型...")
    
    try:
        from ultralytics import YOLO
        
        # 下载预训练模型
        model = YOLO('yolov8n-pose.pt')
        print("YOLOv8 pose模型准备完成")
        return True
    except Exception as e:
        print(f"YOLOv8模型下载失败: {e}")
        return False


def create_sample_data():
    """创建示例数据"""
    print("创建示例数据...")
    
    try:
        # 运行数据创建脚本
        script_path = os.path.join("data", "training_data", "create_training_data.py")
        subprocess.check_call([sys.executable, script_path, "--mode", "sample"])
        print("示例数据创建完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"示例数据创建失败: {e}")
        return False


def test_camera():
    """测试摄像头"""
    print("测试摄像头...")
    
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print("摄像头测试成功")
                cap.release()
                return True
            else:
                print("摄像头无法读取帧")
        else:
            print("无法打开摄像头")
        
        cap.release()
        return False
    except Exception as e:
        print(f"摄像头测试失败: {e}")
        return False


def test_microphone():
    """测试麦克风"""
    print("测试麦克风...")
    
    try:
        import pyaudio
        
        p = pyaudio.PyAudio()
        
        # 检查可用的音频设备
        device_count = p.get_device_count()
        print(f"发现 {device_count} 个音频设备")
        
        # 查找默认输入设备
        default_input = p.get_default_input_device_info()
        print(f"默认输入设备: {default_input['name']}")
        
        p.terminate()
        print("麦克风测试成功")
        return True
    except Exception as e:
        print(f"麦克风测试失败: {e}")
        print("注意: 语音功能可能无法正常使用")
        return False


def create_run_script():
    """创建运行脚本"""
    print("创建运行脚本...")
    
    # Windows批处理脚本
    if platform.system() == "Windows":
        script_content = """@echo off
echo 启动工人操作视频打分工具...
cd /d "%~dp0"
python src/main.py
pause
"""
        with open("run.bat", "w", encoding="utf-8") as f:
            f.write(script_content)
        print("创建了 run.bat")
    
    # Unix shell脚本
    script_content = """#!/bin/bash
echo "启动工人操作视频打分工具..."
cd "$(dirname "$0")"
python src/main.py
"""
    with open("run.sh", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    # 添加执行权限
    if platform.system() != "Windows":
        os.chmod("run.sh", 0o755)
        print("创建了 run.sh")


def main():
    """主安装函数"""
    print("=" * 60)
    print("工人操作视频打分工具 - 安装程序")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 创建目录
    setup_directories()
    
    # 安装依赖
    if not install_requirements():
        print("安装失败: 依赖包安装错误")
        return False
    
    # 下载模型
    if not download_yolo_model():
        print("警告: YOLOv8模型下载失败，姿态检测功能可能无法使用")
    
    # 创建示例数据
    if not create_sample_data():
        print("警告: 示例数据创建失败")
    
    # 测试硬件
    camera_ok = test_camera()
    mic_ok = test_microphone()
    
    # 创建运行脚本
    create_run_script()
    
    print("\n" + "=" * 60)
    print("安装完成!")
    print("=" * 60)
    
    print("\n系统状态:")
    print(f"  摄像头: {'✓' if camera_ok else '✗'}")
    print(f"  麦克风: {'✓' if mic_ok else '✗'}")
    
    print("\n使用方法:")
    print("  1. 交互式模式: python src/main.py")
    print("  2. 快速启动: ./run.sh (Linux/Mac) 或 run.bat (Windows)")
    print("  3. 演示模式: python src/main.py --mode demo")
    
    print("\n注意事项:")
    if not camera_ok:
        print("  - 请检查摄像头连接和权限")
    if not mic_ok:
        print("  - 请检查麦克风连接和权限")
        print("  - 可能需要安装额外的音频驱动")
    
    print("\n如需帮助，请查看 README.md 文件")
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
