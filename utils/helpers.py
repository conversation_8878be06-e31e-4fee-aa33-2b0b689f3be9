"""
辅助函数模块
提供通用的工具函数
"""

import os
import json
import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime


def ensure_dir(directory: str):
    """确保目录存在"""
    os.makedirs(directory, exist_ok=True)


def load_json(filepath: str) -> Dict:
    """
    加载JSON文件
    
    Args:
        filepath: 文件路径
        
    Returns:
        JSON数据字典
    """
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"文件不存在: {filepath}")
        return {}
    except json.JSONDecodeError as e:
        print(f"JSON格式错误: {e}")
        return {}


def save_json(data: Dict, filepath: str):
    """
    保存数据到JSON文件
    
    Args:
        data: 要保存的数据
        filepath: 文件路径
    """
    ensure_dir(os.path.dirname(filepath))
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


def calculate_angle(p1: Tuple[float, float], 
                   p2: Tuple[float, float], 
                   p3: Tuple[float, float]) -> float:
    """
    计算三点之间的角度
    
    Args:
        p1: 第一个点
        p2: 顶点
        p3: 第三个点
        
    Returns:
        角度（度）
    """
    v1 = np.array([p1[0] - p2[0], p1[1] - p2[1]])
    v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]])
    
    cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
    cos_angle = np.clip(cos_angle, -1.0, 1.0)
    
    angle = np.arccos(cos_angle)
    return np.degrees(angle)


def calculate_distance(p1: Tuple[float, float], p2: Tuple[float, float]) -> float:
    """
    计算两点之间的欧几里得距离
    
    Args:
        p1: 第一个点
        p2: 第二个点
        
    Returns:
        距离
    """
    return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)


def normalize_keypoints(keypoints: Dict[str, Tuple[float, float]], 
                       reference_points: List[str] = ['left_shoulder', 'right_shoulder']) -> Dict[str, Tuple[float, float]]:
    """
    标准化关键点坐标
    
    Args:
        keypoints: 关键点字典
        reference_points: 参考点列表
        
    Returns:
        标准化后的关键点
    """
    # 找到有效的参考点
    ref_points = []
    for ref in reference_points:
        if ref in keypoints:
            ref_points.append(keypoints[ref])
    
    if len(ref_points) < 2:
        # 如果参考点不足，使用所有可用点的中心
        if keypoints:
            points = list(keypoints.values())
            center_x = sum(p[0] for p in points) / len(points)
            center_y = sum(p[1] for p in points) / len(points)
            ref_center = (center_x, center_y)
        else:
            ref_center = (0, 0)
        scale = 1.0
    else:
        # 使用肩膀中点作为参考中心
        ref_center = ((ref_points[0][0] + ref_points[1][0]) / 2,
                     (ref_points[0][1] + ref_points[1][1]) / 2)
        # 使用肩宽作为缩放参考
        scale = calculate_distance(ref_points[0], ref_points[1])
        if scale == 0:
            scale = 1.0
    
    # 标准化所有关键点
    normalized = {}
    for joint, (x, y) in keypoints.items():
        norm_x = (x - ref_center[0]) / scale
        norm_y = (y - ref_center[1]) / scale
        normalized[joint] = (norm_x, norm_y)
    
    return normalized


def smooth_pose_sequence(pose_sequence: List[Dict[str, Tuple[float, float]]], 
                        window_size: int = 3) -> List[Dict[str, Tuple[float, float]]]:
    """
    平滑姿态序列
    
    Args:
        pose_sequence: 姿态序列
        window_size: 平滑窗口大小
        
    Returns:
        平滑后的姿态序列
    """
    if len(pose_sequence) < window_size:
        return pose_sequence
    
    smoothed_sequence = []
    half_window = window_size // 2
    
    for i in range(len(pose_sequence)):
        # 确定窗口范围
        start_idx = max(0, i - half_window)
        end_idx = min(len(pose_sequence), i + half_window + 1)
        
        # 获取窗口内的姿态
        window_poses = pose_sequence[start_idx:end_idx]
        
        # 计算平均姿态
        smoothed_pose = {}
        if window_poses:
            # 获取所有关节点
            all_joints = set()
            for pose in window_poses:
                all_joints.update(pose.keys())
            
            for joint in all_joints:
                joint_positions = []
                for pose in window_poses:
                    if joint in pose:
                        joint_positions.append(pose[joint])
                
                if joint_positions:
                    avg_x = sum(pos[0] for pos in joint_positions) / len(joint_positions)
                    avg_y = sum(pos[1] for pos in joint_positions) / len(joint_positions)
                    smoothed_pose[joint] = (avg_x, avg_y)
        
        smoothed_sequence.append(smoothed_pose)
    
    return smoothed_sequence


def visualize_pose_comparison(standard_pose: Dict[str, Tuple[float, float]], 
                            actual_pose: Dict[str, Tuple[float, float]], 
                            save_path: Optional[str] = None):
    """
    可视化姿态对比
    
    Args:
        standard_pose: 标准姿态
        actual_pose: 实际姿态
        save_path: 保存路径
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    # 绘制标准姿态
    if standard_pose:
        x_coords = [pos[0] for pos in standard_pose.values()]
        y_coords = [pos[1] for pos in standard_pose.values()]
        ax1.scatter(x_coords, y_coords, c='blue', s=50, alpha=0.7)
        ax1.set_title('标准姿态')
        ax1.set_aspect('equal')
        ax1.grid(True, alpha=0.3)
    
    # 绘制实际姿态
    if actual_pose:
        x_coords = [pos[0] for pos in actual_pose.values()]
        y_coords = [pos[1] for pos in actual_pose.values()]
        ax2.scatter(x_coords, y_coords, c='red', s=50, alpha=0.7)
        ax2.set_title('实际姿态')
        ax2.set_aspect('equal')
        ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        ensure_dir(os.path.dirname(save_path))
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    else:
        plt.show()
    
    plt.close()


def create_evaluation_report_html(evaluation_data: Dict, output_path: str):
    """
    创建HTML格式的评估报告
    
    Args:
        evaluation_data: 评估数据
        output_path: 输出路径
    """
    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>工人操作评估报告</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
            .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .score { font-size: 24px; font-weight: bold; color: #2c3e50; }
            .grade { font-size: 18px; color: #27ae60; }
            .recommendations { background-color: #fff3cd; padding: 10px; border-radius: 3px; }
            .details { background-color: #f8f9fa; padding: 10px; border-radius: 3px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>工人操作评估报告</h1>
            <p>生成时间: {timestamp}</p>
        </div>
        
        <div class="section">
            <h2>综合评估结果</h2>
            <div class="score">综合评分: {overall_score:.3f}</div>
            <div class="grade">评分等级: {grade}</div>
        </div>
        
        <div class="section">
            <h2>分项评分</h2>
            <p><strong>动作评分:</strong> {action_score:.3f}</p>
            <p><strong>语音评分:</strong> {voice_score:.3f}</p>
        </div>
        
        <div class="section">
            <h2>改进建议</h2>
            <div class="recommendations">
                <ul>
                {recommendations}
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h2>详细结果</h2>
            <div class="details">
                <pre>{details}</pre>
            </div>
        </div>
    </body>
    </html>
    """
    
    # 提取数据
    overall = evaluation_data.get('overall_evaluation', {})
    timestamp = datetime.fromtimestamp(evaluation_data.get('timestamp', 0)).strftime('%Y-%m-%d %H:%M:%S')
    
    if overall.get('success'):
        overall_score = overall.get('overall_score', 0)
        grade = overall.get('grade', '未知')
        action_score = overall.get('action_score', 0)
        voice_score = overall.get('voice_score', 0)
        recommendations = '\n'.join([f'<li>{rec}</li>' for rec in overall.get('recommendations', [])])
    else:
        overall_score = 0
        grade = '评估失败'
        action_score = 0
        voice_score = 0
        recommendations = '<li>评估未完成</li>'
    
    details = json.dumps(evaluation_data, ensure_ascii=False, indent=2)
    
    # 生成HTML
    html_content = html_template.format(
        timestamp=timestamp,
        overall_score=overall_score,
        grade=grade,
        action_score=action_score,
        voice_score=voice_score,
        recommendations=recommendations,
        details=details
    )
    
    ensure_dir(os.path.dirname(output_path))
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)


def resize_image(image: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
    """
    调整图像大小
    
    Args:
        image: 输入图像
        target_size: 目标大小 (width, height)
        
    Returns:
        调整后的图像
    """
    return cv2.resize(image, target_size)


def add_text_to_image(image: np.ndarray, text: str, position: Tuple[int, int], 
                     font_scale: float = 0.7, color: Tuple[int, int, int] = (255, 255, 255),
                     thickness: int = 2) -> np.ndarray:
    """
    在图像上添加文字
    
    Args:
        image: 输入图像
        text: 文字内容
        position: 文字位置
        font_scale: 字体大小
        color: 文字颜色
        thickness: 线条粗细
        
    Returns:
        添加文字后的图像
    """
    img_copy = image.copy()
    cv2.putText(img_copy, text, position, cv2.FONT_HERSHEY_SIMPLEX, 
                font_scale, color, thickness)
    return img_copy


def create_progress_bar(current: int, total: int, width: int = 50) -> str:
    """
    创建进度条字符串
    
    Args:
        current: 当前进度
        total: 总数
        width: 进度条宽度
        
    Returns:
        进度条字符串
    """
    if total == 0:
        return "[" + "=" * width + "] 100%"
    
    progress = current / total
    filled = int(width * progress)
    bar = "=" * filled + "-" * (width - filled)
    percentage = int(progress * 100)
    
    return f"[{bar}] {percentage}%"


if __name__ == "__main__":
    # 测试辅助函数
    print("测试辅助函数...")
    
    # 测试角度计算
    angle = calculate_angle((0, 0), (1, 0), (1, 1))
    print(f"角度计算测试: {angle}度")
    
    # 测试距离计算
    distance = calculate_distance((0, 0), (3, 4))
    print(f"距离计算测试: {distance}")
    
    # 测试进度条
    for i in range(0, 101, 10):
        progress = create_progress_bar(i, 100)
        print(f"进度条测试: {progress}")
    
    print("辅助函数测试完成")
